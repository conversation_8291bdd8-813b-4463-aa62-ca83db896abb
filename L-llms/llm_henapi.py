import requests
import os
from groq import Groq
from dotenv import load_dotenv

load_dotenv()

api_key = os.environ.get("HDGSB_API_KEY")


url = "https://api.hdgsb.com/v1/images/edits"

payload={}
files=[
   ('image',('31225951_59371037e9_small.png',open('cmMtdXBsb2FkLTE2ODc4MzMzNDc3NTEtMjA=/31225951_59371037e9_small.png','rb'),'image/png'))
]
headers = {
   'Authorization': 'Bearer {{api_key}}'
}

response = requests.request("POST", url, headers=headers, data=payload, files=files)

print(response.text)