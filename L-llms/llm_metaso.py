import requests
import json
import os
from dotenv import load_dotenv
load_dotenv()
print(os.environ.get("METASO_API_KEY"))

"""
秘塔搜索API-免费100次/天
"""




url = 'https://metaso.cn/api/open/search/v2'
params = {
'question':'编程工具trae 最新新闻'
}

headers = {
    'Authorization': 'Bearer ' + os.environ.get("METASO_API_KEY"),
    'Content-Type': 'application/json',
    'Connection': 'keep-alive'
}

response = requests.post(url, data=json.dumps(params), headers=headers)
print(response.text)
