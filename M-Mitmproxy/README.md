# 微信公众号文章数据获取工具

基于mitmproxy的微信公众号文章阅读量等数据获取工具。

## 核心文件

- `wechat_key_extractor.py` - 微信认证信息提取器（主程序）
- `gzh_collector.py` - 公众号文章数据收集器
- `config/interceptor.py` - mitmproxy拦截脚本
- `config.json` - 配置文件（包含认证信息）
- `requirements.txt` - 依赖包列表

## 安装依赖

```bash
pip install -r requirements.txt
```

## 使用方法

### 1. 提取微信认证信息

```python
from wechat_key_extractor import WeChatKeyExtractor

extractor = WeChatKeyExtractor()
extractor.start_proxy()  # 启动代理
extractor.setup_system_proxy()  # 配置系统代理

# 在微信中访问公众号文章，自动提取认证信息
credentials = extractor.get_credentials()
```

### 2. 获取文章数据

```python
from gzh_collector import GZHArticle

gzh = GZHArticle(
    appmsg_token="your_token",
    appmsg_cookie="your_cookies"
)

article_url = "https://mp.weixin.qq.com/s?__biz=xxx&mid=xxx&idx=1&sn=xxx"
data = gzh.get_appmsgext(article_url)
print(f"阅读量: {data['read_num']}")
```

## 注意事项

1. 需要在macOS系统上运行
2. 需要配置系统代理
3. 认证信息有时效性，需要定期更新
4. 只能获取已关注公众号的文章数据
