#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
macOS微信客户端内置浏览器Key提取器
使用mitmproxy中间人代理技术拦截微信请求，提取appmsg_token和Cookie
"""

import os
import json
import time
import subprocess
from pathlib import Path
from mitmproxy import ctx, http
from mitmproxy.tools.main import mitmdump
import urllib.parse
from http.cookies import SimpleCookie


class WeChatKeyExtractor:
    """微信Key提取器主类"""

    def __init__(self, config_dir="./config"):
        self.config_dir = Path(config_dir)
        self.config_dir.mkdir(exist_ok=True)
        self.config_file = self.config_dir / "wechat_credentials.json"
        self.proxy_port = 8080
        self.is_running = False
        self.proxy_process = None

    def setup_proxy_script(self):
        """设置代理拦截脚本"""
        script_content = '''
import urllib.parse
import json
import os
import time
from mitmproxy import ctx, http
from http.cookies import SimpleCookie

class WeChatInterceptor:
    def __init__(self):
        self.config_file = "config/wechat_credentials.json"
        os.makedirs("config", exist_ok=True)

    def response(self, flow: http.HTTPFlow) -> None:
        """拦截微信相关请求"""
        url = urllib.parse.unquote(flow.request.url)

        # 拦截微信公众号文章页面请求
        if "mp.weixin.qq.com/s" in url:
            self._extract_credentials(flow, url)

        # 拦截getappmsgext API请求
        elif "mp.weixin.qq.com/mp/getappmsgext" in url:
            self._log_api_request(flow, url)

    def _extract_credentials(self, flow, url):
        """从请求中提取认证信息"""
        try:
            # 获取请求头
            headers = {}
            for key, value in flow.request.headers.items():
                headers[key] = value

            # 获取Cookie
            cookies_str = "; ".join([f"{key}={value}" for key, value in flow.request.cookies.items()])

            # 解析appmsg_token
            cookie_obj = SimpleCookie()
            cookie_obj.load(cookies_str)
            appmsg_token = cookie_obj.get('appmsg_token')
            appmsg_token_value = appmsg_token.value if appmsg_token else None

            if appmsg_token_value:
                # 保存认证信息
                credentials = {
                    "appmsg_token": appmsg_token_value,
                    "cookies": cookies_str,
                    "headers": headers,
                    "extracted_time": time.time(),
                    "source_url": url
                }

                with open(self.config_file, 'w', encoding='utf-8') as f:
                    json.dump(credentials, f, ensure_ascii=False, indent=4)

                ctx.log.info(f"✅ 成功提取微信认证信息: {appmsg_token_value[:20]}...")

        except Exception as e:
            ctx.log.error(f"❌ 提取认证信息失败: {e}")

    def _log_api_request(self, flow, url):
        """记录API请求信息"""
        try:
            response_data = flow.response.json()
            ctx.log.info(f"📊 API响应: {json.dumps(response_data, ensure_ascii=False)[:200]}...")
        except:
            pass

addons = [WeChatInterceptor()]
'''

        script_file = self.config_dir / "interceptor.py"
        with open(script_file, 'w', encoding='utf-8') as f:
            f.write(script_content)

        return script_file

    def start_proxy(self):
        """启动代理服务器"""
        script_file = self.setup_proxy_script()

        try:
            # 启动mitmproxy
            args = [
                'mitmdump',
                '-p', str(self.proxy_port),
                '-s', str(script_file),
                '--set', 'confdir=' + str(self.config_dir),
                '--ssl-insecure'  # 忽略SSL证书验证
            ]

            print(f"🚀 启动代理服务器，端口: {self.proxy_port}")
            print(f"📁 配置目录: {self.config_dir}")

            # 使用subprocess.Popen启动后台进程
            self.proxy_process = subprocess.Popen(
                args,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )
            self.is_running = True

            # 等待一下确保进程启动
            time.sleep(2)

            # 检查进程是否正常运行
            if self.proxy_process.poll() is None:
                print("✅ 代理服务器启动成功")
                return True
            else:
                _, stderr = self.proxy_process.communicate()
                print(f"❌ 代理服务器启动失败: {stderr}")
                return False

        except Exception as e:
            print(f"❌ 代理服务器启动失败: {e}")
            return False

    def setup_system_proxy(self):
        """配置系统代理设置（macOS）"""
        try:
            # 获取当前网络服务
            result = subprocess.run(['networksetup', '-listallnetworkservices'],
                                  capture_output=True, text=True)
            services = [line.strip() for line in result.stdout.split('\n')
                       if line.strip() and not line.startswith('*')]

            # 为每个网络服务设置HTTP代理
            for service in services:
                if service and 'Wi-Fi' in service:  # 主要针对Wi-Fi
                    subprocess.run(['networksetup', '-setwebproxy', service,
                                  '127.0.0.1', str(self.proxy_port)])
                    subprocess.run(['networksetup', '-setsecurewebproxy', service,
                                  '127.0.0.1', str(self.proxy_port)])
                    print(f"✅ 已为 {service} 设置代理")

            print(f"🔧 系统代理已配置: 127.0.0.1:{self.proxy_port}")
            print("⚠️  请在微信中访问公众号文章以触发认证信息提取")

        except Exception as e:
            print(f"❌ 配置系统代理失败: {e}")
            print("请手动在系统偏好设置中配置HTTP代理:")
            print(f"   地址: 127.0.0.1")
            print(f"   端口: {self.proxy_port}")

    def cleanup_system_proxy(self):
        """清理系统代理设置"""
        try:
            result = subprocess.run(['networksetup', '-listallnetworkservices'],
                                  capture_output=True, text=True)
            services = [line.strip() for line in result.stdout.split('\n')
                       if line.strip() and not line.startswith('*')]

            for service in services:
                if service and 'Wi-Fi' in service:
                    subprocess.run(['networksetup', '-setwebproxystate', service, 'off'])
                    subprocess.run(['networksetup', '-setsecurewebproxystate', service, 'off'])

            print("🧹 系统代理设置已清理")

        except Exception as e:
            print(f"❌ 清理系统代理失败: {e}")

    def stop_proxy(self):
        """停止代理服务器"""
        if self.proxy_process and self.proxy_process.poll() is None:
            try:
                self.proxy_process.terminate()
                self.proxy_process.wait(timeout=5)
                print("🛑 代理服务器已停止")
            except subprocess.TimeoutExpired:
                self.proxy_process.kill()
                print("🛑 代理服务器已强制停止")
            except Exception as e:
                print(f"❌ 停止代理服务器失败: {e}")
        self.is_running = False

    def get_credentials(self):
        """获取已提取的认证信息"""
        if not self.config_file.exists():
            return None

        try:
            with open(self.config_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            print(f"❌ 读取认证信息失败: {e}")
            return None

    def test_credentials(self, article_url):
        """测试认证信息是否有效"""
        credentials = self.get_credentials()
        if not credentials:
            print("❌ 未找到认证信息")
            return False

        try:
            from gzh_collector import GZHArticle

            gzh = GZHArticle(
                appmsg_token=credentials['appmsg_token'],
                appmsg_cookie=credentials['cookies']
            )

            result = gzh.get_appmsgext(article_url)
            if result and 'read_num' in result:
                print(f"✅ 认证信息有效，测试文章阅读量: {result['read_num']}")
                return True
            else:
                print("❌ 认证信息可能已过期")
                return False

        except Exception as e:
            print(f"❌ 测试认证信息失败: {e}")
            return False


def main():
    """主函数 - 演示完整的提取流程"""
    extractor = WeChatKeyExtractor()

    try:
        print("🎯 微信Key提取器启动")
        print("=" * 50)

        # 启动代理服务器
        proxy_started = extractor.start_proxy()
        if not proxy_started:
            print("❌ 代理服务器启动失败，程序退出")
            return

        # 配置系统代理
        extractor.setup_system_proxy()

        print("\n📱 请按以下步骤操作:")
        print("1. 打开微信客户端")
        print("2. 访问任意公众号文章")
        print("3. 等待认证信息自动提取")
        print("4. 按 Ctrl+C 停止程序")

        # 监控认证信息提取
        while True:
            time.sleep(5)
            credentials = extractor.get_credentials()
            if credentials:
                print(f"\n✅ 已提取认证信息:")
                print(f"   Token: {credentials['appmsg_token'][:20]}...")
                print(f"   提取时间: {time.ctime(credentials['extracted_time'])}")

                # 可选：测试认证信息
                test_url = input("\n🔍 输入测试文章URL (回车跳过): ").strip()
                if test_url:
                    extractor.test_credentials(test_url)
                break
            else:
                print("⏳ 等待认证信息提取...")

    except KeyboardInterrupt:
        print("\n\n🛑 程序停止")

    finally:
        # 停止代理服务器
        extractor.stop_proxy()
        # 清理系统代理设置
        extractor.cleanup_system_proxy()


if __name__ == "__main__":
    main()
