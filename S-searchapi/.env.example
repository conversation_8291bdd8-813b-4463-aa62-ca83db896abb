# ===== 搜索API配置示例文件 =====
# 复制此文件为 .env 并填入真实的API密钥

# ===== 基础配置 =====
# 搜索服务类型 (必填)
# 可选值: search1api, google, bing, serpapi, serper, duckdu<PERSON><PERSON>, sear<PERSON><PERSON>, brave
SEARCH_SERVICE=brave

# 最大搜索结果数 (可选，默认10)
MAX_RESULTS=10

# 是否爬取完整内容 (可选，默认0不爬取)
# 设置为大于0的数字表示爬取前N个结果的完整内容
CRAWL_RESULTS=0

# ===== Search1API 配置 =====
# Search1API 官网: https://search1api.com/
# 注册后获取API密钥
SEARCH1API_KEY=your_search1api_key_here

# ===== Google 自定义搜索配置 =====
# Google Cloud Console: https://console.cloud.google.com/
# 1. 启用 Custom Search API
# 2. 创建自定义搜索引擎: https://cse.google.com/
# 3. 获取搜索引擎ID (CX) 和API密钥
GOOGLE_KEY=your_google_api_key_here
GOOGLE_CX=your_custom_search_engine_id_here

# ===== Bing 搜索配置 =====
# Microsoft Azure: https://azure.microsoft.com/
# 创建 Bing Search v7 资源获取订阅密钥
BING_KEY=your_bing_subscription_key_here

# ===== SerpAPI 配置 =====
# SerpAPI 官网: https://serpapi.com/
# 注册后获取API密钥，提供Google搜索结果
SERPAPI_KEY=your_serpapi_key_here

# ===== Serper 配置 =====
# Serper 官网: https://serper.dev/
# 注册后获取API密钥，提供Google搜索结果
SERPER_KEY=your_serper_key_here

# ===== DuckDuckGo 配置 =====
# DuckDuckGo 搜索不需要API密钥，但可以配置API服务器
# 如果使用自建的DuckDuckGo API服务，可以设置此URL
DUCKDUCKGO_API_URL=https://ddg.search2ai.online/search

# ===== SearXNG 配置 =====
# SearXNG 是开源的搜索引擎聚合器
# 需要部署自己的SearXNG实例或使用公共实例
# 公共实例列表: https://searx.space/
SEARXNG_BASE_URL=https://searx.example.com

# ===== Brave 搜索配置 =====
# Brave Search API 官网: https://api.search.brave.com/
# 注册后获取API Token，提供独立的搜索结果
# 特点：隐私保护、无广告、独立索引
BRAVE_API_TOKEN=your_brave_api_token_here

# ===== 使用说明 =====
# 1. 选择一个搜索服务，设置对应的API密钥
# 2. 设置 SEARCH_SERVICE 为对应的服务名称
# 3. 根据需要调整 MAX_RESULTS 和 CRAWL_RESULTS
# 4. 运行 python demo.py 进行测试

# ===== Brave 搜索高级配置 =====
# 搜索类型: web(网页搜索) 或 news(新闻搜索)
BRAVE_SEARCH_TYPE=news
# 搜索语言
BRAVE_SEARCH_LANG=zh-hans
# 国家代码
BRAVE_COUNTRY=CN
# 时间范围: all, pd(过去一天), pw(过去一周), pm(过去一月), py(过去一年)
BRAVE_FRESHNESS=pd

# ===== AI服务配置 (新闻文章生成器) =====
# 选择一个AI服务进行文章生成

# OpenAI配置 (优先选择，符合PRD要求)
OPENAI_API_KEY=your_openai_api_key_here
OPENAI_MODEL=gpt-3.5-turbo
OPENAI_MAX_TOKENS=4000
OPENAI_TEMPERATURE=0.7

# Claude配置 (备选)
CLAUDE_API_KEY=your_claude_api_key_here
CLAUDE_MODEL=claude-3-sonnet-20240229
CLAUDE_MAX_TOKENS=3000
CLAUDE_TEMPERATURE=0.7

# Groq配置 (备选)
GROQ_API_KEY=your_groq_api_key_here
GROQ_MODEL=llama3-8b-8192
GROQ_MAX_TOKENS=3000
GROQ_TEMPERATURE=0.7

# Moonshot配置 (备选)
MOONSHOT_API_KEY=your_moonshot_api_key_here
MOONSHOT_MODEL=moonshot-v1-8k
MOONSHOT_MAX_TOKENS=3000
MOONSHOT_TEMPERATURE=0.7

# DeepSeek配置 (备选)
DEEPSEEK_API_KEY=your_deepseek_api_key_here
DEEPSEEK_MODEL=deepseek-chat
DEEPSEEK_MAX_TOKENS=3000
DEEPSEEK_TEMPERATURE=0.7

# ===== API服务配置 =====
API_HOST=0.0.0.0
API_PORT=5000
API_DEBUG=false
MAX_BATCH_SIZE=10

# ===== 新闻文章生成器配置 =====
# 最大新闻数量
NEWS_MAX_COUNT=10
# 文章风格
ARTICLE_STYLE=公众号
# 文章长度
ARTICLE_LENGTH=800-1200字

# ===== AI服务获取方式 =====
# OpenAI: https://platform.openai.com/api-keys
# Claude: https://console.anthropic.com/
# Groq: https://console.groq.com/
# Moonshot: https://platform.moonshot.cn/
# DeepSeek: https://platform.deepseek.com/

# ===== 推荐配置 (优化版本) =====
# 新手推荐: 使用 duckduckgo (免费，无需API密钥)
# 隐私保护: 使用 brave (独立索引，隐私友好)
# 商业使用: 使用 google 或 bing (需要付费API)
# 高级用户: 使用 serpapi 或 serper (专业搜索API服务)
# 新闻文章生成: 使用 brave + openai (最佳组合，符合PRD要求)

# ===== PRD实现说明 =====
# 本配置文件支持PRD要求的核心功能：
# 1. ✅ 输入关键词，检索新闻
# 2. ✅ 将新闻结果统一放入大模型的历史对话当中
# 3. ✅ 最终生成一篇公众号文章
# 4. ✅ 优先使用OpenAI

# ===== 快速开始 =====
# 1. 复制此文件为 .env
# 2. 配置 BRAVE_API_TOKEN (推荐) 或其他搜索服务
# 3. 配置 OPENAI_API_KEY (优先选择)
# 4. 运行: python optimized_news_generator.py
