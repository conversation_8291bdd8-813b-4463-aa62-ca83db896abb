## 业务逻辑：
1. 输入关键词
2. 根据关键词，检索最新新闻消息；
3. 获取得到新闻消息整合一起；
4. 发送给ai大模型，生成一篇公众号文章；


# 搜索增强大语言模型服务 PRD

## 1. 产品概述

### 1.1 产品定义
搜索增强大语言模型服务（Search-Enhanced LLM Service）是一个中间件服务，允许大语言模型（如OpenAI、Gemini、Moonshot等）通过工具调用实现联网搜索功能，获取最新信息，并基于这些信息生成更准确的回答。

### 1.2 产品目标
- 为大语言模型提供实时联网搜索能力
- 支持多种搜索服务提供商
- 提供标准化的API接口，便于集成
- 支持流式输出，提升用户体验
- 支持多种大语言模型平台

### 1.3 目标用户
- 需要联网能力的大语言模型应用开发者
- 需要最新信息的聊天机器人服务提供商
- 需要实时数据的AI助手开发团队

## 2. 功能规格

### 2.1 核心功能

#### 2.1.1 搜索功能
- **功能描述**：允许模型搜索互联网获取最新信息
- **输入参数**：搜索查询字符串
- **输出结果**：结构化的搜索结果（标题、链接、摘要）
- **支持的搜索服务**：Google、Bing、SerpAPI、Serper、DuckDuckGo、SearXNG等

#### 2.1.2 新闻搜索功能
- **功能描述**：允许模型搜索最新新闻
- **输入参数**：新闻搜索查询字符串
- **输出结果**：结构化的新闻搜索结果（标题、链接、摘要）
- **支持的搜索服务**：与搜索功能相同，但专注于新闻类别

#### 2.1.3 网页爬取功能
- **功能描述**：允许模型获取特定网页的详细内容
- **输入参数**：网页URL
- **输出结果**：网页内容的结构化数据
- **服务提供商**：自定义爬虫服务

### 2.2 技术规格

#### 2.2.1 API接口
- **端点**：`/v1/chat/completions`
- **方法**：POST
- **请求格式**：兼容OpenAI Chat API格式
- **响应格式**：兼容OpenAI Chat API格式
- **流式输出**：支持SSE（Server-Sent Events）格式

#### 2.2.2 工具定义
```json
{
  "tools": [
    {
      "type": "function",
      "function": {
        "name": "search",
        "description": "搜索互联网获取信息",
        "parameters": {
          "type": "object",
          "properties": {
            "query": {
              "type": "string",
              "description": "搜索查询"
            }
          },
          "required": ["query"]
        }
      }
    },
    {
      "type": "function",
      "function": {
        "name": "news",
        "description": "搜索最新新闻",
        "parameters": {
          "type": "object",
          "properties": {
            "query": {
              "type": "string",
              "description": "新闻搜索查询"
            }
          },
          "required": ["query"]
        }
      }
    },
    {
      "type": "function",
      "function": {
        "name": "crawler",
        "description": "获取特定网页的内容",
        "parameters": {
          "type": "object",
          "properties": {
            "url": {
              "type": "string",
              "description": "网页URL"
            }
          },
          "required": ["url"]
        }
      }
    }
  ]
}
```

#### 2.2.3 搜索结果格式
```json
{
  "results": [
    {
      "title": "结果标题",
      "link": "结果链接",
      "snippet": "结果摘要"
    },
    // 更多结果...
  ]
}
```

#### 2.2.4 支持的模型
- OpenAI（GPT-3.5、GPT-4等）
- Google Gemini
- Moonshot
- Groq
- 其他兼容OpenAI API的模型

## 3. 工作流程

### 3.1 基本流程
1. 接收客户端请求
2. 添加工具定义
3. 第一次调用大模型
4. 检查模型是否请求搜索
5. 如果请求搜索，执行搜索并获取结果
6. 将搜索结果添加到消息历史
7. 第二次调用大模型
8. 返回最终结果给客户端

### 3.2 详细流程图
```
客户端请求 → 解析请求 → 添加工具定义 → 第一次调用模型
                                       ↓
                                  检查工具调用
                                       ↓
                 否 ←───── 是否需要搜索？ ───→ 是
                 ↓                           ↓
            直接返回结果                执行搜索功能
                                       ↓
                                  整合搜索结果
                                       ↓
                                第二次调用模型
                                       ↓
                                  返回最终结果
```

## 4. 技术实现指南

### 4.1 Python实现要点

#### 4.1.1 服务架构
- 使用FastAPI或Flask构建Web服务
- 实现与Node.js版本相同的路由和处理逻辑
- 支持异步处理以提高性能

#### 4.1.2 请求处理
```python
async def handle_request(request_data, api_base, api_key):
    # 解析请求数据
    messages = request_data.get("messages", [])
    model = request_data.get("model", "gpt-3.5-turbo")
    stream = request_data.get("stream", False)
    max_tokens = request_data.get("max_tokens", 3000)
    
    # 获取最新的用户消息
    user_messages = [msg for msg in messages if msg.get("role") == "user"]
    latest_user_message = user_messages[-1] if user_messages else None
    
    # 检查是否为多模态内容
    is_content_array = isinstance(latest_user_message.get("content"), list) if latest_user_message else False
    
    # 构建请求体
    body = {
        "model": model,
        "messages": messages,
        "max_tokens": max_tokens
    }
    
    # 如果不是多模态内容，添加工具定义
    if not is_content_array:
        body["tools"] = [
            {
                "type": "function",
                "function": {
                    "name": "search",
                    "description": "搜索互联网获取信息",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "query": {
                                "type": "string",
                                "description": "搜索查询"
                            }
                        },
                        "required": ["query"]
                    }
                }
            },
            # 添加news和crawler工具定义...
        ]
        body["tool_choice"] = "auto"
    
    # 第一次调用模型
    # ...
```

#### 4.1.3 搜索功能实现
```python
async def search(query):
    """执行互联网搜索"""
    search_service = os.environ.get("SEARCH_SERVICE", "google")
    max_results = int(os.environ.get("MAX_RESULTS", 10))
    
    results = []
    
    if search_service == "google":
        google_cx = os.environ.get("GOOGLE_CX")
        google_key = os.environ.get("GOOGLE_KEY")
        url = f"https://www.googleapis.com/customsearch/v1?cx={google_cx}&key={google_key}&q={quote(query)}"
        
        async with aiohttp.ClientSession() as session:
            async with session.get(url) as response:
                data = await response.json()
                
                if "items" in data:
                    results = [
                        {
                            "title": item.get("title"),
                            "link": item.get("link"),
                            "snippet": item.get("snippet")
                        }
                        for item in data["items"][:max_results]
                    ]
    
    # 实现其他搜索服务...
    
    return json.dumps({"results": results})
```

#### 4.1.4 工具调用处理
```python
# 检查是否有工具调用
if "tool_calls" in data["choices"][0]["message"]:
    tool_calls = data["choices"][0]["message"]["tool_calls"]
    available_functions = {
        "search": search,
        "news": news,
        "crawler": crawler
    }
    
    for tool_call in tool_calls:
        function_name = tool_call["function"]["name"]
        function_to_call = available_functions.get(function_name)
        function_args = json.loads(tool_call["function"]["arguments"])
        
        if function_name == "search":
            function_response = await function_to_call(function_args["query"])
        elif function_name == "crawler":
            function_response = await function_to_call(function_args["url"])
        elif function_name == "news":
            function_response = await function_to_call(function_args["query"])
        
        messages.append({
            "tool_call_id": tool_call["id"],
            "role": "tool",
            "name": function_name,
            "content": function_response
        })
    
    # 第二次调用模型
    # ...
```

#### 4.1.5 流式输出处理
```python
if stream:
    async def generate():
        async with aiohttp.ClientSession() as session:
            async with session.post(
                f"{api_base}/v1/chat/completions",
                headers={
                    "Content-Type": "application/json",
                    "Authorization": f"Bearer {api_key}"
                },
                json=request_body
            ) as response:
                async for line in response.content:
                    if line:
                        yield line
    
    return StreamingResponse(generate(), media_type="text/event-stream")
else:
    # 处理非流式响应
    # ...
```

### 4.2 环境变量配置

| 变量名 | 描述 | 示例值 |
|--------|------|--------|
| SEARCH_SERVICE | 搜索服务提供商 | google, bing, serpapi, serper, duckduckgo, searxng |
| GOOGLE_CX | Google自定义搜索引擎ID | 123456789:abcdef |
| GOOGLE_KEY | Google API密钥 | AIzaSyA1B2C3D4E5F6G7H8I9J0K |
| SERPAPI_KEY | SerpAPI密钥 | 1a2b3c4d5e6f7g8h9i0j |
| SERPER_KEY | Serper密钥 | 1a2b3c4d5e6f7g8h9i0j |
| BING_KEY | Bing搜索API密钥 | 1a2b3c4d5e6f7g8h9i0j |
| SEARXNG_BASE_URL | SearXNG实例URL | https://searx.example.com |
| MAX_RESULTS | 搜索结果数量 | 10 |
| CRAWL_RESULTS | 是否爬取搜索结果 | 0 或 1 |
| APIBASE | 大模型API基础URL | https://api.openai.com |
| AUTH_KEYS | 认证密钥列表 | key1,key2,key3 |

## 5. 接口规范

### 5.1 请求格式

```json
{
  "model": "gpt-3.5-turbo",
  "messages": [
    {
      "role": "system",
      "content": "你是一个有用的助手。"
    },
    {
      "role": "user",
      "content": "2024年奥运会在哪里举行？"
    }
  ],
  "stream": true,
  "max_tokens": 3000
}
```

### 5.2 响应格式（非流式）

```json
{
  "id": "chatcmpl-123456789",
  "object": "chat.completion",
  "created": 1677858242,
  "model": "gpt-3.5-turbo",
  "choices": [
    {
      "message": {
        "role": "assistant",
        "content": "2024年奥运会将在法国巴黎举行，这是巴黎第三次举办奥运会。比赛将于2024年7月26日至8月11日进行。"
      },
      "finish_reason": "stop",
      "index": 0
    }
  ],
  "usage": {
    "prompt_tokens": 57,
    "completion_tokens": 49,
    "total_tokens": 106
  }
}
```

### 5.3 响应格式（流式）

```
data: {"id":"chatcmpl-123456789","object":"chat.completion.chunk","created":1677858242,"model":"gpt-3.5-turbo","choices":[{"index":0,"delta":{"role":"assistant"},"finish_reason":null}]}

data: {"id":"chatcmpl-123456789","object":"chat.completion.chunk","created":1677858242,"model":"gpt-3.5-turbo","choices":[{"index":0,"delta":{"content":"2024"},"finish_reason":null}]}

data: {"id":"chatcmpl-123456789","object":"chat.completion.chunk","created":1677858242,"model":"gpt-3.5-turbo","choices":[{"index":0,"delta":{"content":"年"},"finish_reason":null}]}

// 更多数据块...

data: {"id":"chatcmpl-123456789","object":"chat.completion.chunk","created":1677858242,"model":"gpt-3.5-turbo","choices":[{"index":0,"delta":{},"finish_reason":"stop"}]}

data: [DONE]
```

## 6. 部署指南

### 6.1 本地部署
1. 克隆代码仓库
2. 安装依赖：`pip install -r requirements.txt`
3. 配置环境变量（创建`.env`文件）
4. 启动服务：`python app.py`

### 6.2 云服务部署
- 支持部署到各种云平台（AWS、Azure、GCP等）
- 支持容器化部署（Docker、Kubernetes）
- 支持Serverless部署（AWS Lambda、Vercel等）

### 6.3 系统要求
- Python 3.8+
- 至少512MB RAM
- 网络连接（用于调用搜索API和大模型API）

## 7. 安全与隐私

### 7.1 认证
- 支持API密钥认证
- 支持多密钥配置（AUTH_KEYS环境变量）

### 7.2 数据处理
- 不存储用户查询和对话历史
- 所有请求都是无状态的
- 遵循数据最小化原则

### 7.3 限流
- 建议实现请求限流机制
- 根据API密钥或IP地址限制请求频率

## 8. 测试案例

### 8.1 基本搜索测试
- 查询：`谁是现任美国总统？`
- 预期行为：模型应调用搜索功能，获取最新信息，并回答问题

### 8.2 新闻搜索测试
- 查询：`最近有什么重大科技新闻？`
- 预期行为：模型应调用新闻搜索功能，获取最新科技新闻，并总结

### 8.3 网页爬取测试
- 查询：`总结这个网页的内容：https://example.com`
- 预期行为：模型应调用爬虫功能，获取网页内容，并进行总结

### 8.4 多步骤搜索测试
- 查询：`比较最新的iPhone和三星旗舰手机的相机性能`
- 预期行为：模型可能进行多次搜索，先找出最新型号，再比较它们的相机性能

## 9. 扩展与优化

### 9.1 功能扩展
- 添加图像搜索功能
- 添加视频搜索功能
- 添加地图和位置搜索功能
- 添加产品比价功能

### 9.2 性能优化
- 实现搜索结果缓存
- 优化流式输出性能
- 实现并行搜索请求

### 9.3 可用性提升
- 提供更详细的错误信息
- 添加请求和响应日志
- 提供管理控制台

## 10. 常见问题与解决方案

### 10.1 搜索服务配置问题
- **问题**：搜索服务返回403错误
- **解决方案**：检查API密钥是否正确，是否有足够的配额

### 10.2 大模型API连接问题
- **问题**：连接大模型API超时
- **解决方案**：检查网络连接，增加超时时间，实现重试机制

### 10.3 流式输出问题
- **问题**：流式输出中断
- **解决方案**：实现断点续传，增加错误处理和重连逻辑

## 11. 附录

### 11.1 完整环境变量列表
（详见4.2节）

### 11.2 API参考
- OpenAI API文档：https://platform.openai.com/docs/api-reference
- Google Custom Search API文档：https://developers.google.com/custom-search/v1/overview
- Bing Search API文档：https://www.microsoft.com/en-us/bing/apis/bing-web-search-api

### 11.3 代码示例
（详见4.1节）