# 搜索API演示程序

这是一个统一的搜索API演示程序，支持多种搜索引擎和服务。

## 功能特性

- 🔍 支持多种搜索引擎：Search1API、Google、Bing、SerpAPI、Serper、DuckDuckGo、SearXNG、Brave
- 🛠️ 统一的搜索接口，易于切换不同的搜索服务
- ⏰ **时间信息支持**：自动提取和格式化搜索结果的时间信息
- 📝 完整的中文注释和文档
- 🧪 内置测试功能，可以快速验证配置
- ⚙️ 灵活的配置选项，支持环境变量配置

## 快速开始

### 方式一：使用Google搜索API（推荐）

**1. 自动配置（推荐）**
```bash
# 安装依赖
pip install requests python-dotenv

# 运行配置助手
python setup_google_api.py
```

**2. 手动配置**
```bash
# 复制配置模板
cp .env.example .env

# 编辑.env文件，设置Google API
# SEARCH_SERVICE=google
# GOOGLE_KEY=your_google_api_key
# GOOGLE_CX=your_search_engine_id
```

**3. 测试配置**
```bash
# 测试Google API配置
python google_api_test.py

# 运行演示程序
python demo.py
```

### 方式二：使用免费搜索（快速体验）

```bash
# 安装依赖
pip install requests python-dotenv duckduckgo-search

# 使用DuckDuckGo（免费，无需API密钥）
echo "SEARCH_SERVICE=duckduckgo" > .env

# 运行测试
python demo.py
```

## 支持的搜索服务

### 1. DuckDuckGo (推荐新手)
- ✅ **免费使用**
- ✅ **无需API密钥**
- ✅ **隐私保护**

```env
SEARCH_SERVICE=duckduckgo
```

### 2. Google 自定义搜索
- 🔑 需要API密钥
- 💰 有免费额度，超出后付费
- 🎯 搜索质量高

```env
SEARCH_SERVICE=google
GOOGLE_KEY=your_google_api_key
GOOGLE_CX=your_custom_search_engine_id
```

### 3. Bing 搜索
- 🔑 需要API密钥
- 💰 付费服务
- 🎯 搜索质量高

```env
SEARCH_SERVICE=bing
BING_KEY=your_bing_subscription_key
```

### 4. SerpAPI
- 🔑 需要API密钥
- 💰 付费服务
- 🎯 专业搜索API，支持多种搜索引擎

```env
SEARCH_SERVICE=serpapi
SERPAPI_KEY=your_serpapi_key
```

### 5. Serper
- 🔑 需要API密钥
- 💰 付费服务
- 🎯 快速的Google搜索API

```env
SEARCH_SERVICE=serper
SERPER_KEY=your_serper_key
```

### 6. Search1API
- 🔑 需要API密钥
- 💰 付费服务
- 🎯 支持内容爬取

```env
SEARCH_SERVICE=search1api
SEARCH1API_KEY=your_search1api_key
CRAWL_RESULTS=3
```

### 7. SearXNG
- 🔑 需要自建或使用公共实例
- ✅ 开源免费
- 🎯 聚合多个搜索引擎

```env
SEARCH_SERVICE=searxng
SEARXNG_BASE_URL=https://searx.example.com
```

### 8. Brave 搜索
- 🔑 需要API密钥
- 💰 付费服务（有免费额度）
- 🛡️ 隐私保护，独立索引
- 🚫 无广告，无跟踪

```env
SEARCH_SERVICE=brave
BRAVE_API_TOKEN=your_brave_api_token
```

## 代码示例

```python
from demo import SearchService

# 初始化搜索服务
search = SearchService()

# 执行搜索
result = search.search("Python编程教程")

# 解析结果
import json
data = json.loads(result)
for item in data["results"]:
    print(f"标题: {item['title']}")
    print(f"链接: {item['link']}")
    print(f"摘要: {item['snippet']}")

    # 显示时间信息
    time_info = item.get('time_info', {})
    if time_info and time_info.get('formatted_time') != '未知时间':
        print(f"时间: {time_info['formatted_time']} ({time_info['relative_time']})")
    else:
        print(f"时间: 未知")
    print("-" * 50)
```

## ⏰ 时间信息功能

### 功能说明
搜索结果现在包含详细的时间信息，支持：
- **自动时间提取**：从搜索结果中自动提取发布时间
- **多格式解析**：支持ISO格式、标准格式、相对时间等多种时间格式
- **智能转换**：自动转换为统一的时间格式
- **相对时间显示**：显示"2小时前"、"3天前"等相对时间

### 时间信息结构
```json
{
  "time_info": {
    "raw_time": "2024-05-26T10:30:00Z",
    "formatted_time": "2024-05-26 10:30:00",
    "relative_time": "2小时前",
    "timestamp": 1716690600
  }
}
```

### 使用示例
```bash
# 运行时间功能演示
python time_feature_demo.py

# 查看带时间信息的搜索结果
python demo.py
```

### 支持的时间格式
- ISO格式：`2024-05-26T10:30:00Z`
- 标准格式：`2024-05-26 10:30:00`
- 日期格式：`2024-05-26`
- 英文格式：`May 26, 2024`
- 相对时间：`2天前`、`1小时前`、`刚刚`

## 配置说明

| 环境变量 | 说明 | 默认值 | 必填 |
|---------|------|--------|------|
| `SEARCH_SERVICE` | 搜索服务类型 | 无 | ✅ |
| `MAX_RESULTS` | 最大结果数 | 10 | ❌ |
| `CRAWL_RESULTS` | 爬取完整内容的结果数 | 0 | ❌ |

## 获取API密钥

### Google 自定义搜索
1. 访问 [Google Cloud Console](https://console.cloud.google.com/)
2. 启用 Custom Search API
3. 创建自定义搜索引擎：[Google CSE](https://cse.google.com/)
4. 获取搜索引擎ID (CX) 和API密钥

### Bing 搜索
1. 访问 [Microsoft Azure](https://azure.microsoft.com/)
2. 创建 Bing Search v7 资源
3. 获取订阅密钥

### Brave 搜索
1. 访问 [Brave Search API](https://api.search.brave.com/)
2. 注册账号并创建应用
3. 获取API Token
4. 查看使用限制和定价

### 其他服务
- [SerpAPI](https://serpapi.com/) - 注册后获取API密钥
- [Serper](https://serper.dev/) - 注册后获取API密钥
- [Search1API](https://search1api.com/) - 注册后获取API密钥

## 故障排除

### 常见问题

1. **ImportError: No module named 'duckduckgo_search'**
   ```bash
   pip install duckduckgo-search
   ```

2. **搜索服务未配置**
   - 检查 `.env` 文件是否存在
   - 确认 `SEARCH_SERVICE` 已设置

3. **API密钥无效**
   - 检查API密钥是否正确
   - 确认API服务是否已启用

## 许可证

MIT License
