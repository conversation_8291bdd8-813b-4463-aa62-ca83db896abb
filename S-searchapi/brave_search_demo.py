#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Brave搜索API演示和测试
展示Brave搜索引擎的特性和功能
"""

import os
import json
import requests
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()


def test_brave_api_connection():
    """测试Brave API连接"""
    print("🔍 测试Brave搜索API连接")
    print("=" * 50)
    
    brave_token = os.getenv('BRAVE_API_TOKEN')
    
    if not brave_token or brave_token == 'your_brave_api_token_here':
        print("❌ 错误：未配置BRAVE_API_TOKEN")
        print("请在.env文件中设置您的Brave API Token")
        print("获取方式：https://api.search.brave.com/")
        return False
    
    try:
        # 发送测试请求
        response = requests.get(
            "https://api.search.brave.com/res/v1/web/search",
            headers={
                "Accept": "application/json",
                "Accept-Encoding": "gzip",
                "X-Subscription-Token": brave_token
            },
            params={
                "q": "test",
                "count": 1
            },
            timeout=10
        )
        
        print(f"📊 响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print("✅ Brave API连接成功！")
            
            # 显示API响应信息
            if "web" in data:
                web_results = data["web"].get("results", [])
                print(f"📄 找到 {len(web_results)} 个搜索结果")
                
                if web_results:
                    first_result = web_results[0]
                    print(f"📝 示例结果:")
                    print(f"   标题: {first_result.get('title', 'N/A')}")
                    print(f"   链接: {first_result.get('url', 'N/A')}")
                    print(f"   描述: {first_result.get('description', 'N/A')[:100]}...")
            
            return True
            
        elif response.status_code == 401:
            print("❌ API认证失败")
            print("请检查BRAVE_API_TOKEN是否正确")
            return False
            
        elif response.status_code == 429:
            print("❌ API请求频率超限")
            print("请稍后重试或检查您的配额")
            return False
            
        else:
            print(f"❌ API请求失败: {response.status_code}")
            print(f"响应内容: {response.text[:200]}...")
            return False
            
    except requests.exceptions.Timeout:
        print("❌ 请求超时")
        return False
        
    except requests.exceptions.ConnectionError:
        print("❌ 连接错误，请检查网络连接")
        return False
        
    except Exception as e:
        print(f"❌ 未知错误: {str(e)}")
        return False


def demonstrate_brave_features():
    """演示Brave搜索的特性"""
    print("\n🛡️ Brave搜索特性演示")
    print("=" * 50)
    
    from demo import SearchService
    
    # 临时设置为Brave搜索
    original_service = os.getenv('SEARCH_SERVICE')
    os.environ['SEARCH_SERVICE'] = 'brave'
    
    try:
        search = SearchService()
        
        # 测试不同类型的搜索
        test_queries = [
            ("技术新闻", "科技类搜索"),
            ("隐私保护", "隐私相关搜索"),
            ("开源软件", "开源项目搜索")
        ]
        
        for query, description in test_queries:
            print(f"\n📋 {description}: '{query}'")
            print("-" * 30)
            
            result = search.search(query)
            
            try:
                data = json.loads(result)
                results = data.get("results", [])
                
                if results:
                    print(f"✅ 找到 {len(results)} 条结果")
                    
                    # 显示前2个结果
                    for i, item in enumerate(results[:2], 1):
                        print(f"\n{i}. 📰 {item.get('title', 'N/A')[:60]}...")
                        print(f"   🔗 {item.get('link', 'N/A')}")
                        print(f"   📝 {item.get('snippet', 'N/A')[:80]}...")
                        
                        # 显示Brave特有信息
                        if item.get('profile'):
                            print(f"   🏢 网站简介: {item.get('profile')[:50]}...")
                        
                        if item.get('language'):
                            print(f"   🌐 语言: {item.get('language')}")
                        
                        # 显示时间信息
                        time_info = item.get('time_info', {})
                        if time_info and time_info.get('formatted_time') != '未知时间':
                            print(f"   ⏰ 时间: {time_info.get('relative_time', 'N/A')}")
                else:
                    print("❌ 未找到搜索结果")
                    
            except json.JSONDecodeError:
                print(f"❌ 结果解析失败: {result}")
    
    finally:
        # 恢复原始设置
        if original_service:
            os.environ['SEARCH_SERVICE'] = original_service
        else:
            os.environ.pop('SEARCH_SERVICE', None)


def compare_search_engines():
    """比较不同搜索引擎的结果"""
    print("\n⚖️ 搜索引擎结果对比")
    print("=" * 50)
    
    query = "人工智能发展趋势"
    print(f"搜索关键词: {query}")
    
    # 测试不同的搜索引擎
    engines = [
        ("brave", "Brave搜索"),
        ("duckduckgo", "DuckDuckGo"),
        ("google", "Google搜索")  # 如果配置了的话
    ]
    
    results_comparison = {}
    
    for engine, name in engines:
        print(f"\n🔍 使用 {name} 搜索...")
        
        # 临时切换搜索引擎
        original_service = os.getenv('SEARCH_SERVICE')
        os.environ['SEARCH_SERVICE'] = engine
        
        try:
            from demo import SearchService
            search = SearchService()
            result = search.search(query)
            
            try:
                data = json.loads(result)
                results = data.get("results", [])
                results_comparison[name] = len(results)
                
                if results:
                    print(f"   ✅ 找到 {len(results)} 条结果")
                    # 显示第一个结果的标题
                    first_title = results[0].get('title', 'N/A')[:50]
                    print(f"   📄 首个结果: {first_title}...")
                else:
                    print(f"   ❌ 未找到结果")
                    
            except json.JSONDecodeError:
                print(f"   ❌ 解析失败")
                results_comparison[name] = 0
                
        except Exception as e:
            print(f"   ❌ 搜索失败: {str(e)}")
            results_comparison[name] = 0
        
        finally:
            # 恢复原始设置
            if original_service:
                os.environ['SEARCH_SERVICE'] = original_service
    
    # 显示对比结果
    print(f"\n📊 结果数量对比:")
    for engine, count in results_comparison.items():
        print(f"   {engine}: {count} 条结果")


def show_brave_advantages():
    """展示Brave搜索的优势"""
    print("\n🌟 Brave搜索优势")
    print("=" * 50)
    
    advantages = [
        "🛡️ 隐私保护：不跟踪用户，不收集个人数据",
        "🚫 无广告：搜索结果纯净，无广告干扰",
        "🔍 独立索引：拥有自己的搜索索引，不依赖其他搜索引擎",
        "⚡ 快速响应：优化的API响应速度",
        "🌐 全球覆盖：支持多语言和地区搜索",
        "📊 透明度：开放的搜索算法和排名机制",
        "💰 合理定价：提供免费额度和灵活的付费计划",
        "🔧 开发友好：简单易用的API接口"
    ]
    
    for advantage in advantages:
        print(f"   {advantage}")
    
    print(f"\n💡 使用建议:")
    print(f"   • 适合注重隐私的用户和应用")
    print(f"   • 适合需要无广告搜索结果的场景")
    print(f"   • 适合开发独立搜索应用")
    print(f"   • 可作为Google搜索的替代方案")


def main():
    """主函数"""
    print("🦁 Brave搜索API演示程序")
    print("=" * 60)
    
    # 检查基本配置
    brave_token = os.getenv('BRAVE_API_TOKEN')
    if not brave_token or brave_token == 'your_brave_api_token_here':
        print("⚠️ 注意：未配置Brave API Token")
        print("请在.env文件中设置BRAVE_API_TOKEN")
        print("获取方式：https://api.search.brave.com/")
        print("\n继续演示其他功能...")
    
    try:
        # 测试API连接
        api_success = test_brave_api_connection()
        
        if api_success:
            # 演示Brave特性
            demonstrate_brave_features()
            
            # 搜索引擎对比
            compare_search_engines()
        
        # 显示Brave优势
        show_brave_advantages()
        
        print(f"\n🎉 演示完成！")
        print(f"\n📚 更多信息:")
        print(f"   • Brave Search API文档: https://api.search.brave.com/app/documentation")
        print(f"   • 配置指南: README.md")
        print(f"   • 使用示例: demo.py")
        
    except KeyboardInterrupt:
        print(f"\n❌ 用户中断执行")
    except Exception as e:
        print(f"❌ 演示过程中出错: {str(e)}")


if __name__ == "__main__":
    main()
