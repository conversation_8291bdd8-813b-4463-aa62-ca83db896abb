import os
import json
import requests
from datetime import datetime, timedelta
from dotenv import load_dotenv

# 加载环境变量配置文件
load_dotenv()

class SearchService:
    """
    统一搜索服务类
    支持多种搜索引擎：Search1API、Google、Bing、SerpAPI、Ser<PERSON>、DuckDuckGo、SearXNG
    """
    def __init__(self):
        """初始化搜索服务"""
        # 从环境变量获取搜索服务类型
        self.search_service = os.getenv('SEARCH_SERVICE')
        # 从环境变量获取最大结果数，默认为10
        self.max_results = int(os.getenv('MAX_RESULTS', 10))

    def _format_time(self, time_str, source="unknown"):
        """
        格式化时间信息

        Args:
            time_str: 原始时间字符串
            source: 时间来源（用于不同的解析策略）

        Returns:
            dict: 包含格式化时间信息的字典
        """
        if not time_str:
            return {
                "raw_time": None,
                "formatted_time": "未知时间",
                "relative_time": "未知",
                "timestamp": None
            }

        try:
            # 根据不同来源解析时间
            parsed_time = None

            if source == "google":
                # Google时间格式处理
                parsed_time = self._parse_google_time(time_str)
            elif source == "bing":
                # Bing时间格式处理
                parsed_time = self._parse_bing_time(time_str)
            elif source == "serpapi":
                # SerpAPI时间格式处理
                parsed_time = self._parse_serpapi_time(time_str)
            elif source == "serper":
                # Serper时间格式处理
                parsed_time = self._parse_serper_time(time_str)
            elif source == "duckduckgo":
                # DuckDuckGo时间格式处理
                parsed_time = self._parse_duckduckgo_time(time_str)
            elif source == "brave":
                # Brave搜索时间格式处理
                parsed_time = self._parse_brave_time(time_str)
            else:
                # 通用时间格式处理
                parsed_time = self._parse_generic_time(time_str)

            if parsed_time:
                return {
                    "raw_time": time_str,
                    "formatted_time": parsed_time.strftime("%Y-%m-%d %H:%M:%S"),
                    "relative_time": self._get_relative_time(parsed_time),
                    "timestamp": int(parsed_time.timestamp())
                }
            else:
                return {
                    "raw_time": time_str,
                    "formatted_time": time_str,
                    "relative_time": "无法解析",
                    "timestamp": None
                }

        except Exception as e:
            return {
                "raw_time": time_str,
                "formatted_time": time_str,
                "relative_time": f"解析错误: {str(e)}",
                "timestamp": None
            }

    def _get_relative_time(self, time_obj):
        """获取相对时间描述"""
        now = datetime.now()
        diff = now - time_obj

        if diff.days > 365:
            years = diff.days // 365
            return f"{years}年前"
        elif diff.days > 30:
            months = diff.days // 30
            return f"{months}个月前"
        elif diff.days > 0:
            return f"{diff.days}天前"
        elif diff.seconds > 3600:
            hours = diff.seconds // 3600
            return f"{hours}小时前"
        elif diff.seconds > 60:
            minutes = diff.seconds // 60
            return f"{minutes}分钟前"
        else:
            return "刚刚"

    def _parse_google_time(self, time_str):
        """解析Google时间格式"""
        # Google可能返回的时间格式
        formats = [
            "%Y-%m-%dT%H:%M:%SZ",
            "%Y-%m-%d",
            "%b %d, %Y"
        ]

        for fmt in formats:
            try:
                return datetime.strptime(time_str, fmt)
            except ValueError:
                continue
        return None

    def _parse_bing_time(self, time_str):
        """解析Bing时间格式"""
        # Bing可能返回的时间格式
        formats = [
            "%Y-%m-%dT%H:%M:%S.%fZ",
            "%Y-%m-%dT%H:%M:%SZ",
            "%Y-%m-%d"
        ]

        for fmt in formats:
            try:
                return datetime.strptime(time_str, fmt)
            except ValueError:
                continue
        return None

    def _parse_serpapi_time(self, time_str):
        """解析SerpAPI时间格式"""
        return self._parse_generic_time(time_str)

    def _parse_serper_time(self, time_str):
        """解析Serper时间格式"""
        return self._parse_generic_time(time_str)

    def _parse_duckduckgo_time(self, time_str):
        """解析DuckDuckGo时间格式"""
        return self._parse_generic_time(time_str)

    def _parse_brave_time(self, time_str):
        """解析Brave搜索时间格式"""
        debug_time = os.getenv('DEBUG_TIME_PARSING', 'false').lower() == 'true'

        if debug_time:
            print(f"🕐 调试：解析Brave时间格式 '{time_str}'")

        # Brave可能返回的时间格式
        formats = [
            # ISO 8601格式
            "%Y-%m-%dT%H:%M:%SZ",
            "%Y-%m-%dT%H:%M:%S.%fZ",
            "%Y-%m-%dT%H:%M:%S%z",
            "%Y-%m-%dT%H:%M:%S.%f%z",

            # 日期格式
            "%Y-%m-%d",
            "%Y/%m/%d",
            "%d/%m/%Y",
            "%m/%d/%Y",

            # 英文日期格式
            "%b %d, %Y",
            "%B %d, %Y",
            "%d %b %Y",
            "%d %B %Y",

            # 带时间的格式
            "%Y-%m-%d %H:%M:%S",
            "%Y-%m-%d %H:%M",
            "%d/%m/%Y %H:%M:%S",
            "%m/%d/%Y %H:%M:%S",

            # RFC 2822格式
            "%a, %d %b %Y %H:%M:%S %z",
            "%a, %d %b %Y %H:%M:%S GMT",

            # 其他常见格式
            "%Y%m%d",
            "%Y%m%d%H%M%S"
        ]

        for fmt in formats:
            try:
                parsed_time = datetime.strptime(time_str, fmt)
                if debug_time:
                    print(f"✅ 成功解析时间，使用格式: {fmt}")
                return parsed_time
            except ValueError:
                continue

        # 尝试处理时区信息
        if 'T' in time_str and ('+' in time_str or time_str.endswith('Z')):
            try:
                # 移除时区信息后重试
                clean_time = time_str.replace('Z', '').split('+')[0].split('-')[0:3]
                clean_time_str = '-'.join(clean_time)

                for fmt in ["%Y-%m-%dT%H:%M:%S", "%Y-%m-%dT%H:%M:%S.%f"]:
                    try:
                        parsed_time = datetime.strptime(clean_time_str, fmt)
                        if debug_time:
                            print(f"✅ 清理时区后成功解析，使用格式: {fmt}")
                        return parsed_time
                    except ValueError:
                        continue
            except:
                pass

        if debug_time:
            print(f"❌ 无法解析时间格式，尝试通用解析")

        # 如果标准格式都无法解析，尝试通用解析
        return self._parse_generic_time(time_str)

    def _parse_generic_time(self, time_str):
        """通用时间格式解析"""
        # 常见的时间格式
        formats = [
            "%Y-%m-%dT%H:%M:%SZ",
            "%Y-%m-%dT%H:%M:%S.%fZ",
            "%Y-%m-%d %H:%M:%S",
            "%Y-%m-%d",
            "%d/%m/%Y",
            "%m/%d/%Y",
            "%b %d, %Y",
            "%B %d, %Y"
        ]

        for fmt in formats:
            try:
                return datetime.strptime(time_str, fmt)
            except ValueError:
                continue

        # 如果都无法解析，尝试相对时间解析
        return self._parse_relative_time(time_str)

    def _parse_relative_time(self, time_str):
        """解析相对时间（如"2天前"、"1小时前"等）"""
        import re

        now = datetime.now()
        time_str = time_str.lower().strip()

        # 匹配各种相对时间格式（中文和英文）
        patterns = [
            # 中文格式
            (r'(\d+)\s*年前', lambda m: now - timedelta(days=int(m.group(1)) * 365)),
            (r'(\d+)\s*个月前', lambda m: now - timedelta(days=int(m.group(1)) * 30)),
            (r'(\d+)\s*天前', lambda m: now - timedelta(days=int(m.group(1)))),
            (r'(\d+)\s*小时前', lambda m: now - timedelta(hours=int(m.group(1)))),
            (r'(\d+)\s*分钟前', lambda m: now - timedelta(minutes=int(m.group(1)))),
            (r'(\d+)\s*秒前', lambda m: now - timedelta(seconds=int(m.group(1)))),
            (r'刚刚|刚才', lambda m: now),
            (r'今天', lambda m: now.replace(hour=12, minute=0, second=0, microsecond=0)),
            (r'昨天', lambda m: now.replace(hour=12, minute=0, second=0, microsecond=0) - timedelta(days=1)),
            (r'前天', lambda m: now.replace(hour=12, minute=0, second=0, microsecond=0) - timedelta(days=2)),

            # 英文格式
            (r'(\d+)\s*years?\s+ago', lambda m: now - timedelta(days=int(m.group(1)) * 365)),
            (r'(\d+)\s*months?\s+ago', lambda m: now - timedelta(days=int(m.group(1)) * 30)),
            (r'(\d+)\s*weeks?\s+ago', lambda m: now - timedelta(weeks=int(m.group(1)))),
            (r'(\d+)\s*days?\s+ago', lambda m: now - timedelta(days=int(m.group(1)))),
            (r'(\d+)\s*hours?\s+ago', lambda m: now - timedelta(hours=int(m.group(1)))),
            (r'(\d+)\s*minutes?\s+ago', lambda m: now - timedelta(minutes=int(m.group(1)))),
            (r'(\d+)\s*seconds?\s+ago', lambda m: now - timedelta(seconds=int(m.group(1)))),

            # 特殊英文格式
            (r'(\d+)\s+week\s+ago', lambda m: now - timedelta(weeks=int(m.group(1)))),
            (r'(\d+)\s+month\s+ago', lambda m: now - timedelta(days=int(m.group(1)) * 30)),
            (r'(\d+)\s+day\s+ago', lambda m: now - timedelta(days=int(m.group(1)))),
            (r'(\d+)\s+hour\s+ago', lambda m: now - timedelta(hours=int(m.group(1)))),

            (r'just\s+now|a\s+moment\s+ago', lambda m: now),
            (r'today', lambda m: now.replace(hour=12, minute=0, second=0, microsecond=0)),
            (r'yesterday', lambda m: now.replace(hour=12, minute=0, second=0, microsecond=0) - timedelta(days=1)),
        ]

        for pattern, func in patterns:
            match = re.search(pattern, time_str)
            if match:
                return func(match)

        return None

    def search(self, query):
        """
        统一搜索接口

        Args:
            query (str): 搜索关键词

        Returns:
            str: JSON格式的搜索结果
        """
        print(f"正在使用查询进行自定义搜索: {json.dumps(query)}")

        try:
            # 根据配置的搜索服务类型调用对应的搜索方法
            if self.search_service == "search1api":
                results = self._search_with_search1api(query)
            elif self.search_service == "google":
                results = self._search_with_google(query)
            elif self.search_service == "bing":
                results = self._search_with_bing(query)
            elif self.search_service == "serpapi":
                results = self._search_with_serpapi(query)
            elif self.search_service == "serper":
                results = self._search_with_serper(query)
            elif self.search_service == "duckduckgo":
                results = self._search_with_duckduckgo(query)
            elif self.search_service == "searxng":
                results = self._search_with_searxng(query)
            elif self.search_service == "brave":
                results = self._search_with_brave(query)
            else:
                error_msg = f"不支持的搜索服务: {self.search_service}"
                print(error_msg)
                return error_msg

            # 格式化返回结果
            data = {"results": results}
            print('自定义搜索服务调用完成')
            return json.dumps(data, ensure_ascii=False, indent=2)

        except Exception as error:
            error_msg = f"在 search 函数中捕获到错误: {error}"
            print(error_msg)
            return error_msg

    def _search_with_search1api(self, query):
        """使用Search1API进行搜索"""
        # 获取Search1API密钥和爬取设置
        search1api_key = os.getenv('SEARCH1API_KEY', '')
        crawl_results = os.getenv('CRAWL_RESULTS', '0')

        response = requests.post(
            'https://api.search1api.com/search/',
            headers={
                "Content-Type": "application/json",
                "Authorization": f"Bearer {search1api_key}" if search1api_key else ''
            },
            json={
                "query": query,
                "max_results": str(self.max_results),
                "crawl_results": crawl_results
            }
        )
        return response.json()

    def _search_with_google(self, query):
        """使用Google自定义搜索API进行搜索"""
        # 获取Google搜索API配置
        google_cx = os.getenv('GOOGLE_CX')  # 自定义搜索引擎ID
        google_key = os.getenv('GOOGLE_KEY')  # Google API密钥

        response = requests.get(
            f"https://www.googleapis.com/customsearch/v1?cx={google_cx}&key={google_key}&q={query}"
        )
        data = response.json()

        # 格式化Google搜索结果
        results = []
        for item in data.get("items", [])[:self.max_results]:
            result = {
                "title": item["title"],
                "link": item["link"],
                "snippet": item["snippet"]
            }

            # 添加时间信息
            if "pagemap" in item and "metatags" in item["pagemap"]:
                # 尝试从meta标签获取时间信息
                metatags = item["pagemap"]["metatags"]
                if metatags:
                    meta = metatags[0]
                    publish_time = (meta.get("article:published_time") or
                                  meta.get("datePublished") or
                                  meta.get("publishedDate") or
                                  meta.get("date"))

                    if publish_time:
                        result["time_info"] = self._format_time(publish_time, "google")

            # 如果没有找到时间信息，设置默认值
            if "time_info" not in result:
                result["time_info"] = self._format_time(None)

            results.append(result)

        return results

    def _search_with_bing(self, query):
        """使用Bing搜索API进行搜索"""
        # 获取Bing API密钥
        bing_key = os.getenv('BING_KEY')

        response = requests.get(
            f"https://api.bing.microsoft.com/v7.0/search?q={query}",
            headers={"Ocp-Apim-Subscription-Key": bing_key}
        )
        data = response.json()

        # 格式化Bing搜索结果
        web_pages = data.get("webPages", {}).get("value", [])
        results = []

        for item in web_pages[:self.max_results]:
            result = {
                "title": item["name"],
                "link": item["url"],
                "snippet": item["snippet"]
            }

            # 添加时间信息
            # Bing可能在dateLastCrawled字段提供时间信息
            publish_time = item.get("dateLastCrawled") or item.get("datePublished")
            if publish_time:
                result["time_info"] = self._format_time(publish_time, "bing")
            else:
                result["time_info"] = self._format_time(None)

            results.append(result)

        return results

    def _search_with_searxng(self, query):
        """使用SearXNG搜索引擎进行搜索"""
        searxng_base_url = os.getenv('SEARXNG_BASE_URL')

        response = requests.get(
            f"{searxng_base_url}/search?q={query}&category=general&format=json"
        )
        data = response.json()

        results = []
        for item in data["results"][:self.max_results]:
            result = {
                "title": item["title"],
                "link": item["url"],
                "snippet": item["content"]
            }

            # 添加时间信息
            # SearXNG可能提供publishedDate字段
            publish_time = item.get("publishedDate") or item.get("pubdate")
            if publish_time:
                result["time_info"] = self._format_time(publish_time, "searxng")
            else:
                result["time_info"] = self._format_time(None)

            results.append(result)

        return results

    def _search_with_serpapi(self, query):
        """使用SerpAPI进行Google搜索"""
        serpapi_key = os.getenv('SERPAPI_KEY')

        response = requests.get(
            "https://serpapi.com/search",
            params={
                "q": query,
                "api_key": serpapi_key,
                "engine": "google",
                "num": self.max_results
            }
        )
        data = response.json()

        results = []
        if "organic_results" in data:
            for item in data["organic_results"][:self.max_results]:
                result = {
                    "title": item.get("title", ""),
                    "link": item.get("link", ""),
                    "snippet": item.get("snippet", "")
                }

                # 添加时间信息
                # SerpAPI可能提供date字段
                publish_time = item.get("date") or item.get("rich_snippet", {}).get("top", {}).get("detected_extensions", {}).get("date")
                if publish_time:
                    result["time_info"] = self._format_time(publish_time, "serpapi")
                else:
                    result["time_info"] = self._format_time(None)

                results.append(result)
        return results

    def _search_with_serper(self, query):
        """使用Serper API进行Google搜索"""
        serper_key = os.getenv('SERPER_KEY')

        response = requests.post(
            "https://google.serper.dev/search",
            headers={
                "X-API-KEY": serper_key,
                "Content-Type": "application/json"
            },
            json={
                "q": query,
                "num": self.max_results
            }
        )
        data = response.json()

        results = []
        if "organic" in data:
            for item in data["organic"][:self.max_results]:
                result = {
                    "title": item.get("title", ""),
                    "link": item.get("link", ""),
                    "snippet": item.get("snippet", "")
                }

                # 添加时间信息
                # Serper可能提供date字段
                publish_time = item.get("date") or item.get("datePublished")
                if publish_time:
                    result["time_info"] = self._format_time(publish_time, "serper")
                else:
                    result["time_info"] = self._format_time(None)

                results.append(result)
        return results

    def _search_with_duckduckgo(self, query):
        """使用DuckDuckGo搜索引擎进行搜索"""
        try:
            from duckduckgo_search import DDGS

            # 使用DDGS进行搜索
            with DDGS() as ddgs:
                results = []
                search_results = ddgs.text(query, max_results=self.max_results)

                for item in search_results:
                    result = {
                        "title": item.get("title", ""),
                        "link": item.get("href", ""),
                        "snippet": item.get("body", "")
                    }

                    # 添加时间信息
                    # DuckDuckGo通常不提供时间信息，使用默认值
                    result["time_info"] = self._format_time(None)

                    results.append(result)
                return results

        except ImportError:
            print("未安装duckduckgo_search库，使用API方式...")
            # 如果没有安装duckduckgo_search库，使用API方式
            ddg_api_url = os.getenv('DUCKDUCKGO_API_URL', 'https://ddg.search2ai.online/search')

            try:
                response = requests.post(
                    ddg_api_url,
                    json={
                        "q": query,
                        "max_results": self.max_results
                    },
                    timeout=10
                )

                if response.status_code == 200:
                    data = response.json()
                    results = []
                    if "results" in data:
                        for item in data["results"][:self.max_results]:
                            result = {
                                "title": item.get("title", ""),
                                "link": item.get("href", ""),
                                "snippet": item.get("body", "")
                            }

                            # 添加时间信息
                            result["time_info"] = self._format_time(None)

                            results.append(result)
                    return results
                else:
                    print(f"API请求失败，状态码: {response.status_code}")
                    return self._get_demo_results(query)

            except Exception as e:
                print(f"API请求出错: {e}")
                return self._get_demo_results(query)

        except Exception as e:
            print(f"DuckDuckGo搜索出错: {e}")
            return self._get_demo_results(query)

    def _get_demo_results(self, query):
        """返回演示用的搜索结果"""
        print("使用演示数据...")

        # 生成一些示例时间
        now = datetime.now()
        demo_times = [
            now - timedelta(hours=2),  # 2小时前
            now - timedelta(days=1),   # 1天前
            now - timedelta(days=7),   # 7天前
        ]

        results = []
        for i in range(min(3, self.max_results)):
            result = {
                "title": f"关于'{query}'的搜索结果 {i+1}",
                "link": f"https://example.com/result{i+1}",
                "snippet": f"这是关于'{query}'的第{i+1}个搜索结果的摘要信息。这是一个演示结果，用于展示搜索功能的基本格式。",
                "time_info": self._format_time(demo_times[i].strftime("%Y-%m-%d %H:%M:%S"), "demo")
            }
            results.append(result)

        return results

    def _search_with_brave(self, query):
        """使用Brave搜索API进行搜索"""
        # 检查是否启用新闻搜索模式
        brave_search_type = os.getenv('BRAVE_SEARCH_TYPE', 'web')  # web 或 news

        if brave_search_type == 'news':
            return self._search_brave_news(query)
        else:
            return self._search_brave_web(query)

    def _search_brave_web(self, query):
        """使用Brave Web搜索API进行搜索"""
        print(f"🦁 开始使用Brave Web搜索API搜索: '{query}'")

        # 获取Brave API配置
        brave_token = os.getenv('BRAVE_API_TOKEN')
        brave_safesearch = os.getenv('BRAVE_SAFESEARCH', 'moderate')  # 安全搜索级别
        brave_search_lang = os.getenv('BRAVE_SEARCH_LANG', 'zh-hans') # 搜索语言
        brave_country = os.getenv('BRAVE_COUNTRY', 'CN')             # 国家代码
        brave_freshness = os.getenv('BRAVE_FRESHNESS', 'all')        # 时间范围
        brave_units = os.getenv('BRAVE_UNITS', 'metric')             # 单位系统

        print(f"📋 Brave搜索配置:")
        print(f"   - 最大结果数: {self.max_results}")
        print(f"   - 安全搜索: {brave_safesearch}")
        print(f"   - 搜索语言: {brave_search_lang}")
        print(f"   - 国家代码: {brave_country}")
        print(f"   - 时间范围: {brave_freshness}")
        print(f"   - 单位系统: {brave_units}")

        if not brave_token or brave_token == 'your_brave_api_token_here':
            print("❌ 错误：未配置BRAVE_API_TOKEN")
            print("请在.env文件中设置您的Brave API Token")
            print("获取方式：https://api.search.brave.com/")
            return self._get_demo_results(query)

        # 构建请求参数
        params = {
            "q": query,
            "count": self.max_results,
            "safesearch": brave_safesearch,
            "search_lang": brave_search_lang,
            "country": brave_country,
            "freshness": brave_freshness,
            "units": brave_units,
            "text_decorations": "true",  # 启用文本装饰
            "spellcheck": "true"         # 启用拼写检查
        }

        headers = {
            "Accept": "application/json",
            "Accept-Encoding": "gzip",
            "X-Subscription-Token": brave_token,
            "User-Agent": "SearchAPI-Demo/1.0"
        }

        print(f"🌐 发送API请求到: https://api.search.brave.com/res/v1/web/search")
        print(f"📤 请求参数: {json.dumps(params, ensure_ascii=False, indent=2)}")

        try:
            response = requests.get(
                "https://api.search.brave.com/res/v1/web/search",
                headers=headers,
                params=params,
                timeout=15  # 增加超时时间
            )

            print(f"📥 API响应状态码: {response.status_code}")
            print(f"📊 响应头信息:")
            for key, value in response.headers.items():
                if key.lower() in ['content-type', 'content-length', 'x-ratelimit-remaining', 'x-ratelimit-reset']:
                    print(f"   {key}: {value}")

            if response.status_code == 200:
                data = response.json()
                print(f"✅ Brave API请求成功")

                # 显示API响应的基本信息
                print(f"📋 API响应概览:")
                print(f"   - 响应类型: {data.get('type', 'N/A')}")

                # 解析查询信息
                query_info = data.get("query", {})
                if query_info:
                    print(f"   - 原始查询: {query_info.get('original', query)}")
                    if query_info.get("altered"):
                        print(f"   - 修正查询: {query_info.get('altered')}")
                    if query_info.get("is_navigational"):
                        print(f"   - 导航查询: {query_info.get('is_navigational')}")
                    if query_info.get("is_geolocal"):
                        print(f"   - 地理查询: {query_info.get('is_geolocal')}")

                if "web" in data:
                    web_data = data["web"]
                    print(f"   - 网页结果数: {len(web_data.get('results', []))}")
                    print(f"   - 家庭友好: {web_data.get('family_friendly', 'N/A')}")

                # 检查其他结果类型
                if "news" in data:
                    news_count = len(data["news"].get("results", []))
                    print(f"   - 新闻结果数: {news_count}")

                if "videos" in data:
                    video_count = len(data["videos"].get("results", []))
                    print(f"   - 视频结果数: {video_count}")

                if "locations" in data:
                    location_count = len(data["locations"].get("results", []))
                    print(f"   - 位置结果数: {location_count}")

                if "infobox" in data:
                    print(f"   - 信息框: 可用")

                results = []
                web_results = data.get("web", {}).get("results", [])

                print(f"🔍 开始解析 {len(web_results)} 个搜索结果...")

                for i, item in enumerate(web_results[:self.max_results], 1):
                    print(f"\n📄 解析第 {i} 个结果:")
                    print(f"   标题: {item.get('title', 'N/A')[:80]}...")
                    print(f"   URL: {item.get('url', 'N/A')}")
                    print(f"   类型: {item.get('type', 'N/A')} / {item.get('subtype', 'N/A')}")

                    result = {
                        "title": item.get("title", ""),
                        "link": item.get("url", ""),
                        "snippet": item.get("description", ""),
                        "type": item.get("type", ""),
                        "subtype": item.get("subtype", ""),
                        "publish_time": item.get("page_age", "")
                    }

                    # 添加时间信息 - 根据文档的字段
                    # publish_time = None
                    # time_sources = ["age", "page_age", "page_fetched"]

                    # for time_field in time_sources:
                    #     if item.get(time_field):
                    #         publish_time = item.get(time_field)
                    #         print(f"   ⏰ 找到时间信息 ({time_field}): {publish_time}")
                    #         break

                    # if publish_time:
                    #     result["time_info"] = self._format_time(publish_time, "brave")
                    #     print(f"   📅 格式化时间: {result['time_info']['formatted_time']} ({result['time_info']['relative_time']})")
                    # else:
                    #     result["time_info"] = self._format_time(None)
                    #     print(f"   ⏰ 未找到时间信息")

                    # 添加Brave特有的额外信息 - 根据文档结构
                    extra_info = []

                    # Profile信息
                    if item.get("profile"):
                        profile = item.get("profile")
                        if isinstance(profile, dict):
                            profile_name = profile.get("name", "")
                            result["profile"] = profile
                            extra_info.append(f"网站: {profile_name}")
                        else:
                            profile_text = str(profile)
                            result["profile"] = profile_text
                            extra_info.append(f"网站简介: {profile_text[:50]}...")

                    # 语言信息
                    if item.get("language"):
                        language = item.get("language")
                        result["language"] = language
                        extra_info.append(f"语言: {language}")

                    # 家庭友好性
                    if item.get("family_friendly") is not None:
                        family_friendly = item.get("family_friendly")
                        result["family_friendly"] = family_friendly
                        extra_info.append(f"家庭友好: {'是' if family_friendly else '否'}")

                    # 是否实时
                    if item.get("is_live") is not None:
                        is_live = item.get("is_live")
                        result["is_live"] = is_live
                        if is_live:
                            extra_info.append("实时内容")

                    # Meta URL信息
                    if item.get("meta_url"):
                        meta_url = item.get("meta_url")
                        result["meta_url"] = meta_url
                        if meta_url.get("netloc"):
                            extra_info.append(f"域名: {meta_url.get('netloc')}")

                    # 缩略图
                    if item.get("thumbnail"):
                        thumbnail = item.get("thumbnail")
                        result["thumbnail"] = thumbnail
                        extra_info.append("有缩略图")

                    # 深度结果
                    if item.get("deep_results"):
                        deep_results = item.get("deep_results")
                        result["deep_results"] = deep_results
                        deep_info = []
                        if deep_results.get("news"):
                            deep_info.append(f"新闻({len(deep_results['news'])})")
                        if deep_results.get("videos"):
                            deep_info.append(f"视频({len(deep_results['videos'])})")
                        if deep_results.get("images"):
                            deep_info.append(f"图片({len(deep_results['images'])})")
                        if deep_info:
                            extra_info.append(f"深度结果: {', '.join(deep_info)}")

                    # 评分信息
                    if item.get("rating"):
                        rating = item.get("rating")
                        result["rating"] = rating
                        rating_value = rating.get("ratingValue", 0)
                        review_count = rating.get("reviewCount", 0)
                        extra_info.append(f"评分: {rating_value} ({review_count}评论)")

                    # 位置信息
                    if item.get("location"):
                        location = item.get("location")
                        result["location"] = location
                        extra_info.append("位置相关")

                    if extra_info:
                        print(f"   ℹ️ 额外信息: {'; '.join(extra_info)}")

                    results.append(result)

                print(f"\n✅ 成功解析 {len(results)} 个搜索结果")
                return results

            elif response.status_code == 401:
                print("❌ 错误：Brave API认证失败")
                print("请检查BRAVE_API_TOKEN是否正确")
                print("Token格式应为: BSA-xxxxxxxxxxxxxxxxxxxxxxx")
                return self._get_demo_results(query)

            elif response.status_code == 429:
                print("❌ 错误：Brave API请求频率超限")
                print("请稍后重试或检查您的API配额")
                # 尝试从响应头获取重试时间
                retry_after = response.headers.get('Retry-After')
                if retry_after:
                    print(f"建议等待时间: {retry_after} 秒")
                return self._get_demo_results(query)

            elif response.status_code == 400:
                print("❌ 错误：请求参数无效")
                print(f"响应内容: {response.text[:300]}...")
                return self._get_demo_results(query)

            else:
                print(f"❌ Brave API请求失败，状态码: {response.status_code}")
                print(f"响应内容: {response.text[:300]}...")
                return self._get_demo_results(query)

        except requests.exceptions.Timeout:
            print("❌ Brave API请求超时 (15秒)")
            print("可能的原因：网络连接慢或API服务器响应慢")
            return self._get_demo_results(query)

        except requests.exceptions.ConnectionError:
            print("❌ Brave API连接错误")
            print("请检查：")
            print("  1. 网络连接是否正常")
            print("  2. 防火墙是否阻止了连接")
            print("  3. API服务器是否可访问")
            return self._get_demo_results(query)

        except json.JSONDecodeError as e:
            print(f"❌ JSON解析错误: {str(e)}")
            print("API返回的不是有效的JSON格式")
            return self._get_demo_results(query)

        except Exception as e:
            print(f"❌ Brave搜索出现未知错误: {str(e)}")
            print(f"错误类型: {type(e).__name__}")
            import traceback
            print(f"详细错误信息: {traceback.format_exc()}")
            return self._get_demo_results(query)

    def _search_brave_news(self, query):
        """使用Brave新闻搜索API进行搜索"""
        print(f"📰 开始使用Brave新闻搜索API搜索: '{query}'")

        # 获取Brave API配置
        brave_token = os.getenv('BRAVE_API_TOKEN')
        brave_search_lang = os.getenv('BRAVE_SEARCH_LANG', 'zh-hans') # 搜索语言
        brave_country = os.getenv('BRAVE_COUNTRY', 'CN')             # 国家代码
        brave_freshness = os.getenv('BRAVE_FRESHNESS', 'all')        # 时间范围
        brave_units = os.getenv('BRAVE_UNITS', 'metric')             # 单位系统

        print(f"📋 Brave新闻搜索配置:")
        print(f"   - 最大结果数: {self.max_results}")
        print(f"   - 搜索语言: {brave_search_lang}")
        print(f"   - 国家代码: {brave_country}")
        print(f"   - 时间范围: {brave_freshness}")
        print(f"   - 单位系统: {brave_units}")

        if not brave_token or brave_token == 'your_brave_api_token_here':
            print("❌ 错误：未配置BRAVE_API_TOKEN")
            print("请在.env文件中设置您的Brave API Token")
            print("获取方式：https://api.search.brave.com/")
            return self._get_demo_results(query)

        # 构建新闻搜索请求参数
        params = {
            "q": query,
            "count": self.max_results,
            "search_lang": brave_search_lang,
            "country": brave_country,
            "freshness": brave_freshness,
            "units": brave_units,
            "text_decorations": "true",  # 启用文本装饰
            "spellcheck": "true"         # 启用拼写检查
        }

        headers = {
            "Accept": "application/json",
            "Accept-Encoding": "gzip",
            "X-Subscription-Token": brave_token,
            "User-Agent": "SearchAPI-Demo/1.0"
        }

        print(f"🌐 发送新闻API请求到: https://api.search.brave.com/res/v1/news/search")
        print(f"📤 请求参数: {json.dumps(params, ensure_ascii=False, indent=2)}")

        try:
            response = requests.get(
                "https://api.search.brave.com/res/v1/news/search",
                headers=headers,
                params=params,
                timeout=15  # 增加超时时间
            )

            print(f"📥 API响应状态码: {response.status_code}")
            print(f"📊 响应头信息:")
            for key, value in response.headers.items():
                if key.lower() in ['content-type', 'content-length', 'x-ratelimit-remaining', 'x-ratelimit-reset']:
                    print(f"   {key}: {value}")

            if response.status_code == 200:
                data = response.json()
                print(f"✅ Brave新闻API请求成功")

                # 显示API响应的基本信息
                print(f"📋 新闻API响应概览:")
                print(f"   - 响应类型: {data.get('type', 'N/A')}")

                # 检查是否有拼写建议
                if data.get("query", {}).get("spellcheck_off") is False:
                    original_query = data.get("query", {}).get("original", query)
                    if original_query != query:
                        print(f"🔤 原始查询: '{original_query}'")

                results = []
                news_results = data.get("results", [])

                print(f"🔍 开始解析 {len(news_results)} 个新闻结果...")

                for i, item in enumerate(news_results[:self.max_results], 1):
                    print(f"\n📰 解析第 {i} 个新闻:")
                    print(f"   标题: {item.get('title', 'N/A')[:80]}...")
                    print(f"   URL: {item.get('url', 'N/A')}")
                    print(f"   类型: {item.get('type', 'N/A')}")

                    result = {
                        "title": item.get("title", ""),
                        "link": item.get("url", ""),
                        "snippet": item.get("description", ""),
                        "type": item.get("type", "")
                    }

                    # 添加新闻特有的时间信息 - 根据文档字段
                    publish_time = None
                    time_sources = ["age", "page_age", "page_fetched"]

                    for time_field in time_sources:
                        if item.get(time_field):
                            publish_time = item.get(time_field)
                            print(f"   ⏰ 找到时间信息 ({time_field}): {publish_time}")
                            break

                    if publish_time:
                        result["time_info"] = self._format_time(publish_time, "brave")
                        print(f"   📅 格式化时间: {result['time_info']['formatted_time']} ({result['time_info']['relative_time']})")
                    else:
                        result["time_info"] = self._format_time(None)
                        print(f"   ⏰ 未找到时间信息")

                    # 添加新闻特有的额外信息 - 根据文档结构
                    extra_info = []

                    # 新闻来源信息
                    if item.get("source"):
                        source = item.get("source")
                        result["source"] = source
                        extra_info.append(f"来源: {source}")

                    # Meta URL信息
                    if item.get("meta_url"):
                        meta_url = item.get("meta_url")
                        result["meta_url"] = meta_url
                        if meta_url.get("netloc"):
                            result["netloc"] = meta_url.get("netloc")
                            if not item.get("source"):  # 如果没有source字段，使用netloc
                                extra_info.append(f"域名: {meta_url.get('netloc')}")

                    # 突发新闻标识
                    if item.get("breaking") is not None:
                        breaking = item.get("breaking")
                        result["breaking"] = breaking
                        if breaking:
                            extra_info.append("突发新闻")

                    # 实时新闻标识
                    if item.get("is_live") is not None:
                        is_live = item.get("is_live")
                        result["is_live"] = is_live
                        if is_live:
                            extra_info.append("实时新闻")

                    # 缩略图信息
                    if item.get("thumbnail"):
                        thumbnail = item.get("thumbnail")
                        result["thumbnail"] = thumbnail
                        if isinstance(thumbnail, dict) and thumbnail.get("src"):
                            result["thumbnail_url"] = thumbnail.get("src")
                            extra_info.append("有缩略图")
                        elif isinstance(thumbnail, str):
                            result["thumbnail_url"] = thumbnail
                            extra_info.append("有缩略图")

                    # 额外摘要
                    if item.get("extra_snippets"):
                        extra_snippets = item.get("extra_snippets")
                        result["extra_snippets"] = extra_snippets
                        extra_info.append(f"额外摘要({len(extra_snippets)})")

                    # 语言信息
                    if item.get("language"):
                        language = item.get("language")
                        result["language"] = language
                        extra_info.append(f"语言: {language}")

                    # 家庭友好性
                    if item.get("family_friendly") is not None:
                        family_friendly = item.get("family_friendly")
                        result["family_friendly"] = family_friendly
                        extra_info.append(f"家庭友好: {'是' if family_friendly else '否'}")

                    if extra_info:
                        print(f"   ℹ️ 新闻信息: {'; '.join(extra_info)}")

                    results.append(result)

                print(f"\n✅ 成功解析 {len(results)} 个新闻结果")
                return results

            elif response.status_code == 401:
                print("❌ 错误：Brave API认证失败")
                print("请检查BRAVE_API_TOKEN是否正确")
                print("Token格式应为: BSA-xxxxxxxxxxxxxxxxxxxxxxx")
                return self._get_demo_results(query)

            elif response.status_code == 429:
                print("❌ 错误：Brave API请求频率超限")
                print("请稍后重试或检查您的API配额")
                # 尝试从响应头获取重试时间
                retry_after = response.headers.get('Retry-After')
                if retry_after:
                    print(f"建议等待时间: {retry_after} 秒")
                return self._get_demo_results(query)

            elif response.status_code == 400:
                print("❌ 错误：请求参数无效")
                print(f"响应内容: {response.text[:300]}...")
                return self._get_demo_results(query)

            else:
                print(f"❌ Brave新闻API请求失败，状态码: {response.status_code}")
                print(f"响应内容: {response.text[:300]}...")
                return self._get_demo_results(query)

        except requests.exceptions.Timeout:
            print("❌ Brave新闻API请求超时 (15秒)")
            print("可能的原因：网络连接慢或API服务器响应慢")
            return self._get_demo_results(query)

        except requests.exceptions.ConnectionError:
            print("❌ Brave新闻API连接错误")
            print("请检查：")
            print("  1. 网络连接是否正常")
            print("  2. 防火墙是否阻止了连接")
            print("  3. API服务器是否可访问")
            return self._get_demo_results(query)

        except json.JSONDecodeError as e:
            print(f"❌ JSON解析错误: {str(e)}")
            print("API返回的不是有效的JSON格式")
            return self._get_demo_results(query)

        except Exception as e:
            print(f"❌ Brave新闻搜索出现未知错误: {str(e)}")
            print(f"错误类型: {type(e).__name__}")
            import traceback
            print(f"详细错误信息: {traceback.format_exc()}")
            return self._get_demo_results(query)


def main():
    """主函数 - 测试搜索功能"""
    print("🔍 搜索API演示程序")
    print("=" * 60)

    # 显示程序信息
    print("📋 程序信息:")
    print("   版本: 2.0")
    print("   支持的搜索引擎: Search1API, Google, Bing, SerpAPI, Serper, DuckDuckGo, SearXNG, Brave")
    print("   当前推荐: Brave搜索 (隐私保护、无广告、独立索引)")
    print()

    # 初始化搜索服务
    search_service = SearchService()

    # 详细配置检查
    print("⚙️ 配置检查:")
    print("-" * 40)

    if not search_service.search_service:
        print("❌ 错误：未配置搜索服务")
        print("请在.env文件中设置SEARCH_SERVICE")
        print("支持的搜索服务：search1api, google, bing, serpapi, serper, duckduckgo, searxng, brave")
        return

    print(f"✅ 搜索服务: {search_service.search_service}")
    print(f"✅ 最大结果数: {search_service.max_results}")

    # 检查调试配置
    verbose_logging = os.getenv('VERBOSE_LOGGING', 'false').lower() == 'true'
    debug_api = os.getenv('DEBUG_API_REQUESTS', 'false').lower() == 'true'
    debug_time = os.getenv('DEBUG_TIME_PARSING', 'false').lower() == 'true'

    print(f"🔧 调试配置:")
    print(f"   详细日志: {'启用' if verbose_logging else '禁用'}")
    print(f"   API调试: {'启用' if debug_api else '禁用'}")
    print(f"   时间解析调试: {'启用' if debug_time else '禁用'}")

    # Brave搜索特定配置检查
    if search_service.search_service == "brave":
        print(f"\n🦁 Brave搜索配置:")
        brave_token = os.getenv('BRAVE_API_TOKEN')
        if brave_token and brave_token != 'your_brave_api_token_here':
            print(f"   ✅ API Token: {brave_token[:10]}...{brave_token[-5:]} (已配置)")
        else:
            print(f"   ❌ API Token: 未配置或使用默认值")

        brave_search_type = os.getenv('BRAVE_SEARCH_TYPE', 'web')
        print(f"   搜索类型: {brave_search_type} ({'网页搜索' if brave_search_type == 'web' else '新闻搜索' if brave_search_type == 'news' else '未知'})")

        if brave_search_type == 'web':
            print(f"   安全搜索: {os.getenv('BRAVE_SAFESEARCH', 'moderate')}")

        print(f"   搜索语言: {os.getenv('BRAVE_SEARCH_LANG', 'zh-hans')}")
        print(f"   国家代码: {os.getenv('BRAVE_COUNTRY', 'CN')}")
        print(f"   时间范围: {os.getenv('BRAVE_FRESHNESS', 'all')}")
        print(f"   单位系统: {os.getenv('BRAVE_UNITS', 'metric')}")

    print("\n" + "=" * 60)

    # 测试查询列表 - 更丰富的测试用例
    test_queries = [
        ("veo3", "🎬 视频技术搜索"),
        # ("人工智能最新发展", "🤖 AI技术搜索"),
        # ("2024年科技趋势", "📈 科技趋势搜索"),
        # ("Python编程教程", "💻 编程学习搜索"),
        # ("隐私保护技术", "🛡️ 隐私技术搜索")
    ]

    total_results = 0
    successful_searches = 0

    for i, (query, description) in enumerate(test_queries, 1):
        print(f"\n🔍 测试 {i}/5: {description}")
        print(f"关键词: '{query}'")
        print("-" * 50)

        try:
            # 记录搜索开始时间
            import time
            start_time = time.time()

            # 执行搜索
            result = search_service.search(query)

            # 记录搜索结束时间
            end_time = time.time()
            search_duration = end_time - start_time

            # 添加请求间隔以避免频率限制
            if i < len(test_queries):  # 如果不是最后一个请求
                print("⏳ 等待2秒以避免API频率限制...")
                time.sleep(2)

            # 解析并显示结果
            if isinstance(result, str):
                # 检查是否包含错误信息
                if "错误" in result or "Error" in result:
                    print(f"❌ 搜索失败：{result}")
                else:
                    # 尝试解析JSON结果
                    try:
                        data = json.loads(result)
                        results = data.get("results", [])

                        if results:
                            successful_searches += 1
                            total_results += len(results)

                            print(f"✅ 搜索成功！")
                            print(f"⏱️ 搜索耗时: {search_duration:.2f} 秒")
                            print(f"📊 找到 {len(results)} 条结果")
                            print()

                            # 显示详细结果
                            for j, item in enumerate(results[:3], 1):  # 显示前3条
                                print(f"📄 结果 {j}:")
                                print(f"   📰 标题: {item.get('title', 'N/A')}")
                                print(f"   🔗 链接: {item.get('link', 'N/A')}")
                                print(f"   📝 摘要: {item.get('snippet', 'N/A')[:120]}...")

                                # 显示时间信息
                                time_info = item.get('time_info', {})
                                if time_info and time_info.get('formatted_time') != '未知时间':
                                    print(f"   ⏰ 时间: {time_info.get('formatted_time', 'N/A')} ({time_info.get('relative_time', 'N/A')})")
                                else:
                                    print(f"   ⏰ 时间: 未知")

                                # 显示Brave特有信息
                                if search_service.search_service == "brave":
                                    extra_info = []
                                    if item.get('profile'):
                                        profile = item.get('profile')
                                        if isinstance(profile, dict):
                                            # 处理字典格式的profile
                                            profile_name = profile.get('name', '')
                                            profile_long_name = profile.get('long_name', '')
                                            if profile_name:
                                                extra_info.append(f"网站: {profile_name}")
                                            elif profile_long_name:
                                                extra_info.append(f"网站: {profile_long_name}")
                                        else:
                                            # 处理字符串格式的profile
                                            profile_text = str(profile)
                                            extra_info.append(f"网站简介: {profile_text[:40]}...")
                                    if item.get('language'):
                                        extra_info.append(f"语言: {item.get('language')}")
                                    if item.get('family_friendly'):
                                        extra_info.append(f"家庭友好: {item.get('family_friendly')}")

                                    if extra_info:
                                        print(f"   ℹ️ 额外信息: {'; '.join(extra_info)}")

                                print()

                            # 如果有更多结果，显示统计
                            if len(results) > 3:
                                print(f"   ... 还有 {len(results) - 3} 条结果未显示")

                        else:
                            print(f"❌ 未找到搜索结果")
                            print(f"⏱️ 搜索耗时: {search_duration:.2f} 秒")

                    except json.JSONDecodeError as e:
                        print(f"❌ JSON解析错误：{e}")
                        print(f"原始结果：{result[:200]}...")
            else:
                print(f"❌ 搜索返回了非字符串结果：{type(result)}")

        except Exception as e:
            print(f"❌ 搜索出错：{str(e)}")
            import traceback
            if verbose_logging:
                print(f"详细错误信息：{traceback.format_exc()}")

        print("-" * 50)

    # 显示总结统计
    print(f"\n📊 搜索统计总结:")
    print("=" * 40)
    print(f"✅ 成功搜索: {successful_searches}/{len(test_queries)}")
    print(f"📄 总结果数: {total_results}")
    print(f"📈 平均每次搜索结果数: {total_results/max(successful_searches, 1):.1f}")
    print(f"🎯 成功率: {(successful_searches/len(test_queries)*100):.1f}%")

    # 显示使用建议
    print(f"\n💡 使用建议:")
    if search_service.search_service == "brave":
        print("   🦁 您正在使用Brave搜索 - 隐私保护的最佳选择")
        print("   🛡️ 无跟踪、无广告、独立索引")
        print("   📚 API文档: https://api.search.brave.com/app/documentation")
    else:
        print(f"   🔄 当前使用: {search_service.search_service}")
        print("   💡 建议尝试Brave搜索以获得更好的隐私保护")

    print(f"\n🔧 配置文件: .env")
    print(f"📖 使用说明: README.md")
    print(f"🎯 专用演示: brave_search_demo.py")

    print(f"\n🎉 演示完成！感谢使用搜索API演示程序")
    print("=" * 60)


if __name__ == "__main__":
    main()