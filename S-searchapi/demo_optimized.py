#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
优化后的新闻文章生成器演示
展示改进的搜索、整合和AI生成功能
"""

import os
import sys
from datetime import datetime
from dotenv import load_dotenv

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from news_article_generator import NewsArticleGenerator

# 加载环境变量
load_dotenv()


def main():
    """主演示函数"""
    print("🚀 优化后的新闻文章生成器演示")
    print("=" * 60)
    print("✨ 新功能特性:")
    print("   🔍 智能搜索查询优化")
    print("   📊 新闻相关性评分和排序")
    print("   📝 结构化内容整合")
    print("   🤖 增强的AI提示词")
    print("   ⚡ OpenAI API优化和重试机制")
    print("   📈 详细的处理日志")
    print()
    
    # 初始化生成器
    try:
        print("🔧 正在初始化新闻文章生成器...")
        generator = NewsArticleGenerator()
        print("✅ 初始化完成！")
    except Exception as e:
        print(f"❌ 初始化失败: {str(e)}")
        return
    
    # 推荐的测试关键词
    recommended_keywords = [
        "人工智能最新突破",
        "新能源汽车发展",
        "ChatGPT应用",
        "量子计算进展",
        "5G技术应用"
    ]
    
    print(f"\n🎯 推荐测试关键词:")
    for i, keyword in enumerate(recommended_keywords, 1):
        print(f"   {i}. {keyword}")
    
    # 用户输入
    try:
        user_input = input(f"\n请输入关键词 (或按回车使用默认关键词 '{recommended_keywords[0]}'): ").strip()
        keyword = user_input if user_input else recommended_keywords[0]
        
        print(f"\n🎯 开始处理关键词: '{keyword}'")
        print("=" * 60)
        
        # 执行完整的文章生成流程
        start_time = datetime.now()
        
        result = generator.generate_article(keyword)
        
        end_time = datetime.now()
        elapsed = (end_time - start_time).total_seconds()
        
        # 显示结果
        if result.get('success'):
            print(f"\n🎉 文章生成成功！")
            print(f"⏱️ 总耗时: {elapsed:.2f} 秒")
            print(f"📊 处理统计:")
            print(f"   - 搜索到新闻: {result['news_count']} 条")
            print(f"   - 文章长度: {result['article_length']} 字符")
            print(f"   - AI服务: {result['ai_service']}")
            
            # 显示文章预览
            article = result['article_content']
            preview_length = 800
            
            print(f"\n📄 生成文章预览:")
            print("=" * 60)
            print(article[:preview_length] + ("..." if len(article) > preview_length else ""))
            print("=" * 60)
            
            # 询问是否保存
            save_choice = input(f"\n💾 是否保存完整文章？(y/n): ").strip().lower()
            
            if save_choice in ['y', 'yes', '是', '']:
                filepath = generator.save_article(result)
                if filepath:
                    print(f"✅ 文章已保存到: {filepath}")
                    
                    # 显示文件内容概览
                    print(f"\n📋 保存的文件包含:")
                    print(f"   - 生成的公众号文章")
                    print(f"   - 新闻来源信息")
                    print(f"   - 处理统计数据")
                    print(f"   - 时间戳和元数据")
            
            # 显示新闻来源分析
            print(f"\n📰 新闻来源分析:")
            news_list = result.get('news_list', [])
            if news_list:
                # 统计来源
                sources = {}
                high_quality_count = 0
                
                for news in news_list:
                    source = news.get('source', '未知来源')
                    sources[source] = sources.get(source, 0) + 1
                    
                    if news.get('relevance_score', 0) >= 70:
                        high_quality_count += 1
                
                print(f"   - 高质量新闻: {high_quality_count}/{len(news_list)} 条")
                
                # 显示主要来源
                top_sources = sorted(sources.items(), key=lambda x: x[1], reverse=True)[:3]
                print(f"   - 主要来源: {', '.join([f'{source}({count}条)' for source, count in top_sources])}")
                
                # 显示时间分布
                recent_count = sum(1 for news in news_list 
                                 if '小时' in news.get('time_info', {}).get('relative_time', '') 
                                 or '分钟' in news.get('time_info', {}).get('relative_time', ''))
                print(f"   - 最新消息: {recent_count} 条 (1天内)")
            
        else:
            print(f"\n❌ 文章生成失败: {result.get('error', '未知错误')}")
            print(f"⏱️ 耗时: {elapsed:.2f} 秒")
            
            # 提供故障排除建议
            print(f"\n🔧 故障排除建议:")
            print(f"   1. 检查网络连接")
            print(f"   2. 确认搜索服务配置 (SEARCH_SERVICE)")
            print(f"   3. 检查AI服务API密钥")
            print(f"   4. 查看详细错误日志")
        
    except KeyboardInterrupt:
        print(f"\n\n👋 用户中断程序")
    except Exception as e:
        print(f"\n❌ 程序执行出错: {str(e)}")
        print(f"\n🔧 请检查:")
        print(f"   - 环境变量配置 (.env文件)")
        print(f"   - 网络连接状态")
        print(f"   - API服务可用性")


def show_config_info():
    """显示配置信息"""
    print("⚙️ 当前配置信息:")
    print("-" * 40)
    
    # 搜索服务
    search_service = os.getenv('SEARCH_SERVICE', '未配置')
    print(f"🔍 搜索服务: {search_service}")
    
    # AI服务
    ai_services = []
    if os.getenv('OPENAI_API_KEY'):
        ai_services.append('OpenAI')
    if os.getenv('CLAUDE_API_KEY'):
        ai_services.append('Claude')
    if os.getenv('GROQ_API_KEY'):
        ai_services.append('Groq')
    if os.getenv('MOONSHOT_API_KEY'):
        ai_services.append('Moonshot')
    if os.getenv('DEEPSEEK_API_KEY'):
        ai_services.append('DeepSeek')
    
    if ai_services:
        print(f"🤖 可用AI服务: {', '.join(ai_services)}")
    else:
        print(f"🤖 AI服务: 未配置 (将使用演示模式)")
    
    # 其他配置
    max_results = os.getenv('MAX_RESULTS', '10')
    article_style = os.getenv('ARTICLE_STYLE', '公众号')
    article_length = os.getenv('ARTICLE_LENGTH', '800-1200字')
    
    print(f"📊 最大新闻数: {max_results}")
    print(f"📝 文章风格: {article_style}")
    print(f"📏 文章长度: {article_length}")
    print()


if __name__ == "__main__":
    # 显示配置信息
    show_config_info()
    
    # 运行主程序
    main()
    
    print(f"\n💡 提示:")
    print(f"   - 如需配置API密钥，请编辑 .env 文件")
    print(f"   - 更多测试功能请运行: python test_news_generator.py")
    print(f"   - 查看完整文档: README.md")
    print(f"\n👋 感谢使用新闻文章生成器！")
