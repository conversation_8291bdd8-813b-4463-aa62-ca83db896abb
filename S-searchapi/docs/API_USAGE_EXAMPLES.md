# 新闻文章生成器API使用指南

## API服务启动

### 1. 安装依赖

```bash
pip install flask
```

### 2. 启动服务

```bash
python news_article_api.py
```

默认服务地址：`http://localhost:5000`

### 3. 环境变量配置

```bash
# API服务配置
API_HOST=0.0.0.0
API_PORT=5000
API_DEBUG=false
MAX_BATCH_SIZE=10
```

## API接口文档

### 1. 健康检查

**接口**: `GET /health`

**响应**:
```json
{
  "status": "healthy",
  "timestamp": "2024-01-01T12:00:00",
  "service": "news-article-generator"
}
```

### 2. 获取配置信息

**接口**: `GET /config`

**响应**:
```json
{
  "status": "success",
  "config": {
    "search_service": "brave",
    "ai_service": "demo",
    "max_news_count": 10,
    "article_style": "公众号",
    "article_length": "800-1200字"
  },
  "timestamp": "2024-01-01T12:00:00"
}
```

### 3. 生成单篇文章

**接口**: `POST /generate`

**请求体**:
```json
{
  "keyword": "人工智能",
  "save_file": true
}
```

**响应**:
```json
{
  "status": "success",
  "data": {
    "keyword": "人工智能",
    "article": "# 人工智能发展趋势...",
    "metadata": {
      "news_count": 10,
      "article_length": 1200,
      "ai_service": "demo",
      "elapsed_time": 2.5,
      "timestamp": "2024-01-01T12:00:00"
    },
    "file_path": "api_output/article_人工智能_20240101_120000.md"
  },
  "timestamp": "2024-01-01T12:00:00"
}
```

### 4. 批量生成文章

**接口**: `POST /batch`

**请求体**:
```json
{
  "keywords": ["人工智能", "新能源汽车", "区块链技术"],
  "save_files": true
}
```

**响应**:
```json
{
  "status": "success",
  "data": {
    "results": [
      {
        "keyword": "人工智能",
        "article": "# 人工智能发展趋势...",
        "metadata": {
          "news_count": 10,
          "article_length": 1200,
          "ai_service": "demo",
          "elapsed_time": 2.5
        },
        "file_path": "batch_api_output/article_人工智能_20240101_120000.md"
      }
    ],
    "summary": {
      "total": 3,
      "success": 2,
      "failed": 1
    }
  },
  "timestamp": "2024-01-01T12:00:00"
}
```

### 5. 下载文件

**接口**: `GET /download/<filename>`

**示例**: `GET /download/article_人工智能_20240101_120000.md`

## 使用示例

### Python客户端示例

```python
import requests
import json

# API基础URL
BASE_URL = "http://localhost:5000"

def test_api():
    """测试API功能"""
    
    # 1. 健康检查
    response = requests.get(f"{BASE_URL}/health")
    print("健康检查:", response.json())
    
    # 2. 获取配置
    response = requests.get(f"{BASE_URL}/config")
    print("配置信息:", response.json())
    
    # 3. 生成单篇文章
    data = {
        "keyword": "人工智能",
        "save_file": True
    }
    response = requests.post(f"{BASE_URL}/generate", json=data)
    result = response.json()
    print("生成文章:", result['status'])
    
    if result['status'] == 'success':
        print(f"文章长度: {result['data']['metadata']['article_length']}")
        print(f"新闻数量: {result['data']['metadata']['news_count']}")
    
    # 4. 批量生成
    batch_data = {
        "keywords": ["新能源汽车", "区块链技术"],
        "save_files": True
    }
    response = requests.post(f"{BASE_URL}/batch", json=batch_data)
    batch_result = response.json()
    print("批量生成:", batch_result['data']['summary'])

if __name__ == "__main__":
    test_api()
```

### JavaScript客户端示例

```javascript
const BASE_URL = 'http://localhost:5000';

// 生成单篇文章
async function generateArticle(keyword) {
    try {
        const response = await fetch(`${BASE_URL}/generate`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                keyword: keyword,
                save_file: true
            })
        });
        
        const result = await response.json();
        
        if (result.status === 'success') {
            console.log('文章生成成功:', result.data.metadata);
            return result.data.article;
        } else {
            console.error('生成失败:', result.error);
            return null;
        }
    } catch (error) {
        console.error('请求失败:', error);
        return null;
    }
}

// 批量生成文章
async function batchGenerate(keywords) {
    try {
        const response = await fetch(`${BASE_URL}/batch`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                keywords: keywords,
                save_files: true
            })
        });
        
        const result = await response.json();
        console.log('批量生成结果:', result.data.summary);
        return result.data.results;
    } catch (error) {
        console.error('批量生成失败:', error);
        return null;
    }
}

// 使用示例
generateArticle('人工智能').then(article => {
    if (article) {
        console.log('生成的文章:', article.substring(0, 200) + '...');
    }
});

batchGenerate(['新能源汽车', '区块链技术']).then(results => {
    if (results) {
        results.forEach((result, index) => {
            if (result.article) {
                console.log(`文章 ${index + 1}:`, result.keyword);
            }
        });
    }
});
```

### cURL命令示例

```bash
# 健康检查
curl -X GET http://localhost:5000/health

# 获取配置
curl -X GET http://localhost:5000/config

# 生成单篇文章
curl -X POST http://localhost:5000/generate \
  -H "Content-Type: application/json" \
  -d '{"keyword": "人工智能", "save_file": true}'

# 批量生成
curl -X POST http://localhost:5000/batch \
  -H "Content-Type: application/json" \
  -d '{"keywords": ["新能源汽车", "区块链技术"], "save_files": true}'

# 下载文件
curl -X GET http://localhost:5000/download/article_人工智能_20240101_120000.md \
  -o downloaded_article.md
```

## 错误处理

### 常见错误码

- `400` - 请求参数错误
- `404` - 接口或文件不存在
- `500` - 服务器内部错误

### 错误响应格式

```json
{
  "status": "error",
  "error": "错误描述",
  "timestamp": "2024-01-01T12:00:00"
}
```

## 性能优化

### 1. 并发限制

API服务默认支持多个并发请求，但建议：
- 单个客户端不超过5个并发请求
- 批量生成不超过10个关键词

### 2. 缓存策略

- 相同关键词的文章会重新生成
- 建议客户端实现缓存机制

### 3. 超时设置

- 单篇文章生成：建议超时30秒
- 批量生成：建议超时60秒

## 部署建议

### 1. 生产环境

```bash
# 使用gunicorn部署
pip install gunicorn
gunicorn -w 4 -b 0.0.0.0:5000 news_article_api:app
```

### 2. Docker部署

```dockerfile
FROM python:3.9-slim

WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt

COPY . .
EXPOSE 5000

CMD ["gunicorn", "-w", "4", "-b", "0.0.0.0:5000", "news_article_api:app"]
```

### 3. 负载均衡

建议使用Nginx进行负载均衡和反向代理。

## 监控和日志

### 1. 健康检查

定期调用 `/health` 接口监控服务状态。

### 2. 日志记录

API服务会记录：
- 请求日志
- 错误日志
- 性能指标

### 3. 指标监控

建议监控：
- 请求响应时间
- 成功率
- 错误率
- 并发数

## 安全考虑

### 1. 访问控制

生产环境建议添加：
- API密钥认证
- IP白名单
- 请求频率限制

### 2. 输入验证

- 关键词长度限制
- 特殊字符过滤
- SQL注入防护

### 3. 文件安全

- 文件名安全检查
- 路径遍历防护
- 文件大小限制
