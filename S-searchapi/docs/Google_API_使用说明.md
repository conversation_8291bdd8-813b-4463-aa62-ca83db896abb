# Google API 使用说明

## 🎯 目标

将搜索服务从DuckDuckGo切换到Google自定义搜索API，获得更高质量的搜索结果。

## 📋 准备工作

### 需要的信息
1. **Google API密钥** - 从Google Cloud Console获取
2. **自定义搜索引擎ID** - 从Google CSE获取

### 预计时间
- 首次配置：15-20分钟
- 后续使用：即时

## 🚀 三种配置方式

### 方式一：自动配置助手（推荐）

```bash
# 1. 运行配置助手
python setup_google_api.py

# 2. 按照提示输入API密钥和搜索引擎ID
# 3. 自动生成配置文件
# 4. 自动测试配置
```

**优点**：
- ✅ 自动验证输入格式
- ✅ 自动生成配置文件
- ✅ 自动测试配置
- ✅ 提供详细的错误提示

### 方式二：手动编辑配置文件

```bash
# 1. 复制配置模板
cp .env.example .env

# 2. 编辑.env文件
nano .env

# 3. 修改以下内容：
SEARCH_SERVICE=google
GOOGLE_KEY=你的API密钥
GOOGLE_CX=你的搜索引擎ID

# 4. 测试配置
python google_api_test.py
```

### 方式三：直接修改现有配置

如果您已经有.env文件，只需要修改这几行：

```env
# 将搜索服务改为google
SEARCH_SERVICE=google

# 添加Google API配置
GOOGLE_KEY=AIzaSyABC123DEF456GHI789JKL012MNO345PQR
GOOGLE_CX=abc123def456:ghi789jkl012
```

## 🔑 获取API密钥详细步骤

### 第一步：创建Google Cloud项目

1. 访问 [Google Cloud Console](https://console.cloud.google.com/)
2. 点击"选择项目" → "新建项目"
3. 输入项目名称，点击"创建"

### 第二步：启用Custom Search API

1. 在左侧菜单选择"API和服务" → "库"
2. 搜索"Custom Search API"
3. 点击进入，然后点击"启用"

### 第三步：创建API密钥

1. 选择"API和服务" → "凭据"
2. 点击"+ 创建凭据" → "API密钥"
3. 复制生成的API密钥（格式：AIzaSy...）

### 第四步：创建自定义搜索引擎

1. 访问 [Google CSE](https://cse.google.com/)
2. 点击"添加"
3. 在"要搜索的网站"输入：`*`
4. 输入搜索引擎名称，点击"创建"
5. 在控制面板中复制"搜索引擎ID"

## 🧪 测试配置

### 运行测试脚本

```bash
python google_api_test.py
```

### 预期输出

```
🔍 检查环境变量配置...
✅ SEARCH_SERVICE 配置正确
✅ GOOGLE_KEY 已配置
✅ GOOGLE_CX 已配置

🌐 测试Google API连接...
📡 发送测试请求到: https://www.googleapis.com/customsearch/v1
🔍 测试查询: test
📊 响应状态码: 200
✅ API连接成功！找到 1 个结果

🧪 测试完整搜索功能...
🔍 执行搜索: Python编程
✅ 搜索功能正常！找到 10 个结果

🎉 恭喜！Google API配置完全正确！
```

## 🔧 常见问题解决

### 问题1：API密钥无效

**错误信息**：`API访问被拒绝`

**解决方案**：
1. 检查API密钥是否正确复制
2. 确认Custom Search API已启用
3. 检查API密钥是否有使用限制

### 问题2：搜索引擎ID错误

**错误信息**：`API请求错误`

**解决方案**：
1. 确认搜索引擎ID格式正确（包含`:`）
2. 检查是否使用了正确的Google账号
3. 重新创建搜索引擎

### 问题3：超出配额

**错误信息**：`超出免费配额`

**解决方案**：
1. 检查当前使用量
2. 等待配额重置（每天重置）
3. 考虑启用付费计划

## 💰 费用说明

### 免费配额
- **每天**：100次免费搜索
- **每月**：约3000次免费搜索

### 付费价格
- **超出免费额度**：每1000次搜索 $5 USD
- **计费周期**：按月计费

### 费用控制
1. 在Google Cloud Console设置预算警报
2. 监控API使用量
3. 设置每日配额限制

## 📊 性能对比

| 搜索服务 | 结果质量 | 速度 | 费用 | 配置难度 |
|---------|---------|------|------|----------|
| DuckDuckGo | ⭐⭐⭐ | ⭐⭐⭐⭐ | 免费 | 简单 |
| Google API | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐⭐ | 有限免费 | 中等 |

## 🎉 配置完成后

### 立即可用的功能

```bash
# 运行演示程序
python demo.py

# 运行使用示例
python example_usage.py

# 在代码中使用
python -c "
from demo import SearchService
search = SearchService()
result = search.search('Python教程')
print(result)
"
```

### 高级功能

1. **批量搜索**：支持同时搜索多个关键词
2. **结果过滤**：根据关键词过滤搜索结果
3. **错误处理**：自动处理API错误和网络问题
4. **配置灵活**：随时切换不同的搜索服务

## 📞 获取帮助

### 文档资源
- `README.md` - 项目总体说明
- `Google_API_配置指南.md` - 详细配置步骤
- `完成总结.md` - 项目功能总结

### 测试工具
- `google_api_test.py` - 配置测试工具
- `setup_google_api.py` - 配置助手
- `demo.py` - 功能演示

### 在线资源
- [Google Custom Search API文档](https://developers.google.com/custom-search/v1/overview)
- [Google Cloud Console](https://console.cloud.google.com/)
- [Google CSE](https://cse.google.com/)

---

**配置完成后，您将拥有一个功能强大的Google搜索API集成！** 🚀
