# Google搜索API配置指南

## 📋 概述

本指南将详细介绍如何配置Google自定义搜索API，包括获取API密钥和创建自定义搜索引擎的完整步骤。

## 🚀 快速开始

### 第一步：创建Google Cloud项目

1. **访问Google Cloud Console**
   - 打开 [https://console.cloud.google.com/](https://console.cloud.google.com/)
   - 使用您的Google账号登录

2. **创建新项目**
   - 点击顶部的项目选择器
   - 点击"新建项目"
   - 输入项目名称（例如：`my-search-api`）
   - 点击"创建"

### 第二步：启用Custom Search API

1. **进入API库**
   - 在左侧菜单中选择"API和服务" > "库"
   - 搜索"Custom Search API"
   - 点击"Custom Search API"
   - 点击"启用"

2. **等待启用完成**
   - 通常需要几分钟时间
   - 启用后会自动跳转到API详情页面

### 第三步：创建API密钥

1. **进入凭据页面**
   - 在左侧菜单中选择"API和服务" > "凭据"
   - 点击"+ 创建凭据"
   - 选择"API密钥"

2. **配置API密钥**
   - 系统会生成一个API密钥
   - **重要**：立即复制并保存这个密钥
   - 建议点击"限制密钥"进行安全配置

3. **限制API密钥（推荐）**
   - 选择"限制密钥"
   - 在"API限制"中选择"限制密钥"
   - 选择"Custom Search API"
   - 点击"保存"

### 第四步：创建自定义搜索引擎

1. **访问Google CSE**
   - 打开 [https://cse.google.com/](https://cse.google.com/)
   - 使用同一个Google账号登录

2. **创建搜索引擎**
   - 点击"添加"或"创建自定义搜索引擎"
   - 在"要搜索的网站"中输入：`*`（搜索整个网络）
   - 输入搜索引擎名称（例如：`我的搜索引擎`）
   - 点击"创建"

3. **获取搜索引擎ID**
   - 创建完成后，点击"控制面板"
   - 在"基本信息"中找到"搜索引擎ID"
   - 复制这个ID（格式类似：`abc123def456:ghi789jkl012`）

### 第五步：配置环境变量

1. **编辑.env文件**
   ```env
   # 设置使用Google搜索
   SEARCH_SERVICE=google
   
   # 替换为您的实际API密钥
   GOOGLE_KEY=AIzaSyABC123DEF456GHI789JKL012MNO345PQR
   
   # 替换为您的搜索引擎ID
   GOOGLE_CX=abc123def456:ghi789jkl012
   
   # 其他配置
   MAX_RESULTS=10
   CRAWL_RESULTS=0
   ```

2. **保存文件**
   - 确保文件名为`.env`
   - 确保API密钥和搜索引擎ID正确无误

## 🧪 测试配置

### 运行测试程序

```bash
# 测试Google搜索API
python demo.py
```

### 预期输出

```
=== 搜索API演示程序 ===
当前使用的搜索服务：google
最大结果数：10
--------------------------------------------------

测试 1: 搜索关键词 'Python编程教程'
------------------------------
正在使用查询进行自定义搜索: "Python编程教程"
自定义搜索服务调用完成
✅ 找到 10 条结果：

1. 标题：Python编程入门教程 - 菜鸟教程
   链接：https://www.runoob.com/python/python-tutorial.html
   摘要：Python 是一个高层次的结合了解释性、编译性、互动性和面向对象的脚本语言...
```

## 💰 费用说明

### 免费额度
- **每天免费搜索次数**：100次
- **每月免费搜索次数**：约3000次
- 对于个人学习和小型项目完全够用

### 付费计划
- **超出免费额度后**：每1000次搜索 $5 USD
- **计费方式**：按实际使用量计费
- **费用控制**：可在Google Cloud Console设置预算警报

## 🔧 高级配置

### 1. 搜索引擎优化

**编辑搜索引擎设置**：
1. 访问 [https://cse.google.com/](https://cse.google.com/)
2. 选择您的搜索引擎
3. 点击"设置" > "基本信息"

**推荐设置**：
- **搜索整个网络**：启用
- **图片搜索**：启用
- **安全搜索**：中等
- **语言**：中文（简体）

### 2. API密钥安全

**IP限制**（推荐）：
1. 在Google Cloud Console中编辑API密钥
2. 选择"应用限制" > "IP地址"
3. 添加您的服务器IP地址

**HTTP引用站点限制**：
1. 选择"应用限制" > "HTTP引用站点"
2. 添加您的域名（如：`*.yourdomain.com/*`）

### 3. 监控和日志

**启用API监控**：
1. 访问"API和服务" > "监控"
2. 查看API使用情况和错误率
3. 设置警报通知

## ❗ 常见问题

### Q1: API密钥无效
**解决方案**：
- 检查API密钥是否正确复制
- 确认Custom Search API已启用
- 检查API密钥限制设置

### Q2: 搜索引擎ID错误
**解决方案**：
- 确认搜索引擎ID格式正确
- 检查是否使用了正确的Google账号
- 重新创建搜索引擎

### Q3: 超出配额限制
**解决方案**：
- 检查当前使用量
- 考虑启用付费计划
- 优化搜索频率

### Q4: 搜索结果质量不佳
**解决方案**：
- 调整搜索引擎设置
- 优化搜索关键词
- 启用更多搜索选项

## 📞 技术支持

### Google官方文档
- [Custom Search API文档](https://developers.google.com/custom-search/v1/overview)
- [Google Cloud Console帮助](https://cloud.google.com/docs)

### 社区支持
- [Stack Overflow](https://stackoverflow.com/questions/tagged/google-custom-search)
- [Google开发者社区](https://developers.google.com/community)

## 🔄 备用方案

如果Google API配置遇到问题，可以临时切换到其他搜索服务：

```env
# 临时使用DuckDuckGo
SEARCH_SERVICE=duckduckgo

# 或使用其他API服务
# SEARCH_SERVICE=bing
# BING_KEY=your_bing_key
```

---

**配置完成后，您就可以使用高质量的Google搜索API了！** 🎉
