# 新闻文章生成器

根据PRD需求实现的新闻搜索和AI文章生成功能。

## 功能概述

新闻文章生成器实现了完整的业务流程：

1. **输入关键词** - 用户提供搜索关键词
2. **检索最新新闻** - 使用多种搜索引擎API获取相关新闻
3. **整合新闻内容** - 将搜索到的新闻进行结构化整合
4. **AI生成文章** - 使用大语言模型生成公众号风格文章

## 核心特性

### 🔍 多搜索引擎支持
- **Brave搜索** (推荐) - 隐私保护，独立索引
- **Google搜索** - 高质量结果
- **Bing搜索** - 微软搜索引擎
- **DuckDuckGo** - 免费，无需API密钥
- **SerpAPI/Serper** - 专业搜索API服务
- **SearXNG** - 开源搜索聚合器

### 🤖 多AI服务支持
- **OpenAI** (GPT-3.5/GPT-4) - 业界领先
- **Claude** (Anthropic) - 高质量对话AI
- **Groq** - 高速推理
- **Moonshot** (月之暗面) - 中文优化
- **DeepSeek** - 国产AI服务
- **演示模式** - 无需API密钥的模拟生成

### 📰 智能新闻处理
- 自动搜索最新相关新闻
- 智能内容整合和去重
- 时间信息提取和格式化
- 来源信息标注

### ✍️ 专业文章生成
- 公众号风格文章创作
- 可自定义文章长度和风格
- 结构化内容组织
- 原创性内容生成

## 快速开始

### 1. 环境配置

```bash
# 复制配置文件
cp .env.example .env

# 编辑配置文件，填入API密钥
vim .env
```

### 2. 基本配置

在 `.env` 文件中配置：

```bash
# 搜索服务 (推荐使用brave)
SEARCH_SERVICE=brave
BRAVE_API_TOKEN=your_brave_api_token_here
BRAVE_SEARCH_TYPE=news

# AI服务 (选择一个)
OPENAI_API_KEY=your_openai_api_key_here
# 或者
CLAUDE_API_KEY=your_claude_api_key_here

# 文章生成配置
NEWS_MAX_COUNT=10
ARTICLE_STYLE=公众号
ARTICLE_LENGTH=800-1200字
```

### 3. 运行示例

```bash
# 基本使用
python news_article_generator.py

# 使用示例
python news_article_example.py

# 运行测试
python test_news_article.py
```

## 使用方法

### 基础用法

```python
from news_article_generator import NewsArticleGenerator

# 初始化生成器
generator = NewsArticleGenerator()

# 生成文章
result = generator.generate_article("人工智能")

if result['success']:
    print(f"文章生成成功!")
    print(f"文章内容: {result['article_content']}")
    
    # 保存文章
    filepath = generator.save_article(result)
    print(f"文章已保存到: {filepath}")
else:
    print(f"生成失败: {result['error']}")
```

### 批量生成

```python
keywords = ["人工智能", "新能源汽车", "区块链技术"]

for keyword in keywords:
    result = generator.generate_article(keyword)
    if result['success']:
        generator.save_article(result, output_dir="batch_output")
```

### 自定义配置

```python
import os

# 临时修改配置
os.environ['NEWS_MAX_COUNT'] = '5'
os.environ['ARTICLE_STYLE'] = '科技博客'
os.environ['ARTICLE_LENGTH'] = '500-800字'

# 重新初始化生成器
generator = NewsArticleGenerator()
```

## API接口

### 生成文章

```python
def generate_article(keyword: str) -> dict:
    """
    生成新闻文章
    
    Args:
        keyword: 搜索关键词
        
    Returns:
        {
            'success': bool,           # 是否成功
            'keyword': str,            # 搜索关键词
            'article_content': str,    # 生成的文章内容
            'news_count': int,         # 使用的新闻数量
            'article_length': int,     # 文章字符数
            'ai_service': str,         # 使用的AI服务
            'elapsed_time': float,     # 生成耗时(秒)
            'timestamp': str,          # 生成时间
            'error': str              # 错误信息(失败时)
        }
    """
```

### 保存文章

```python
def save_article(result: dict, output_dir: str = "output") -> str:
    """
    保存生成的文章
    
    Args:
        result: 生成结果
        output_dir: 输出目录
        
    Returns:
        保存的文件路径
    """
```

## 配置说明

### 搜索服务配置

| 服务 | 配置项 | 说明 |
|------|--------|------|
| Brave | `BRAVE_API_TOKEN` | API令牌 |
| Google | `GOOGLE_KEY`, `GOOGLE_CX` | API密钥和搜索引擎ID |
| Bing | `BING_KEY` | 订阅密钥 |
| DuckDuckGo | 无需配置 | 免费使用 |

### AI服务配置

| 服务 | 配置项 | 说明 |
|------|--------|------|
| OpenAI | `OPENAI_API_KEY` | API密钥 |
| Claude | `CLAUDE_API_KEY` | API密钥 |
| Groq | `GROQ_API_KEY` | API密钥 |
| Moonshot | `MOONSHOT_API_KEY` | API密钥 |
| DeepSeek | `DEEPSEEK_API_KEY` | API密钥 |

### 文章生成配置

| 配置项 | 默认值 | 说明 |
|--------|--------|------|
| `NEWS_MAX_COUNT` | 10 | 最大新闻数量 |
| `ARTICLE_STYLE` | 公众号 | 文章风格 |
| `ARTICLE_LENGTH` | 800-1200字 | 文章长度 |

## 输出格式

生成的文章会保存为Markdown格式，包含：

- 基本信息（关键词、生成时间、AI服务等）
- 生成的文章内容
- 新闻来源信息

示例输出文件：`article_人工智能_20240101_120000.md`

## 错误处理

系统包含完善的错误处理机制：

- **搜索失败** - 自动重试或使用备用搜索服务
- **AI服务异常** - 降级到演示模式
- **网络问题** - 超时和重试机制
- **配置错误** - 详细的错误提示

## 性能优化

- **并发搜索** - 支持多线程新闻搜索
- **缓存机制** - 避免重复搜索相同关键词
- **内容去重** - 自动过滤重复新闻
- **智能截断** - 控制输入长度避免AI服务限制

## 最佳实践

### 1. 搜索服务选择
- **新手推荐**: DuckDuckGo (免费)
- **隐私保护**: Brave (独立索引)
- **商业使用**: Google/Bing (高质量)
- **新闻专项**: Brave新闻搜索

### 2. AI服务选择
- **高质量**: OpenAI GPT-4 或 Claude
- **中文优化**: Moonshot 或 DeepSeek
- **高速生成**: Groq
- **成本控制**: GPT-3.5 或演示模式

### 3. 关键词优化
- 使用具体的关键词而非泛泛的词汇
- 结合时间限定词（如"最新"、"2024年"）
- 避免过于宽泛或过于狭窄的关键词

## 故障排除

### 常见问题

1. **搜索无结果**
   - 检查网络连接
   - 验证API密钥配置
   - 尝试更换搜索关键词

2. **AI生成失败**
   - 检查AI服务API密钥
   - 确认API额度充足
   - 尝试降级到演示模式

3. **文章质量不佳**
   - 增加新闻数量 (`NEWS_MAX_COUNT`)
   - 调整文章风格和长度
   - 优化搜索关键词

### 调试模式

```python
import logging
logging.basicConfig(level=logging.DEBUG)

# 启用详细日志
generator = NewsArticleGenerator()
```

## 更新日志

### v1.0.0 (2024-01-01)
- 实现基础新闻搜索功能
- 支持多种搜索引擎
- 集成多个AI服务
- 完整的文章生成流程
- 错误处理和日志记录

## 许可证

本项目遵循MIT许可证。

## 贡献

欢迎提交Issue和Pull Request来改进这个项目。

## 联系方式

如有问题或建议，请通过以下方式联系：
- 提交GitHub Issue
- 发送邮件至项目维护者
