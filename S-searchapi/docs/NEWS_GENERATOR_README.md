# 📰 新闻文章生成器 - 优化版

基于搜索API和AI大模型的智能新闻文章生成工具，专为公众号内容创作优化。

## ✨ 核心功能

### 🔍 智能新闻搜索
- **智能查询优化**: 根据关键词类型自动添加相关搜索词
- **相关性评分**: 对搜索结果进行智能评分和排序
- **多维度筛选**: 基于标题匹配、内容质量、时间新鲜度、来源可信度等因素
- **质量保证**: 自动过滤低质量和不相关的新闻

### 📝 结构化内容整合
- **分级展示**: 按重要性将新闻分为重点、相关、补充三个层级
- **智能摘要**: 自动提取关键信息和数据
- **来源分析**: 统计新闻来源分布和可信度
- **时间分析**: 分析新闻时间分布和新鲜度

### 🤖 增强AI文章生成
- **专业提示词**: 针对公众号文章特点优化的详细提示词
- **多模型支持**: 支持OpenAI、Claude、Groq等多种AI服务
- **重试机制**: 自动重试和错误恢复
- **质量检查**: 生成内容的自动质量验证

### 📊 详细处理日志
- **实时进度**: 详细的处理步骤和进度显示
- **性能统计**: 处理时间、新闻数量、文章质量等统计
- **错误诊断**: 详细的错误信息和故障排除建议

## 🚀 快速开始

### 1. 环境配置

```bash
# 复制配置文件
cp .env.example .env

# 编辑配置文件，设置必要的API密钥
# 最小配置：
SEARCH_SERVICE=duckduckgo  # 免费搜索服务
OPENAI_API_KEY=your_openai_api_key  # AI服务
```

### 2. 安装依赖

```bash
# 安装基础依赖
pip install -r requirements.txt

# 或使用项目依赖管理
pip install -e .
```

### 3. 运行演示

```bash
# 运行优化版演示
python demo_optimized.py

# 运行完整测试
python test_news_generator.py

# 运行原版演示
python news_article_generator.py
```

## 📋 配置说明

### 搜索服务配置

```env
# 推荐配置 (免费)
SEARCH_SERVICE=duckduckgo

# 高质量配置 (需要API密钥)
SEARCH_SERVICE=brave
BRAVE_API_TOKEN=your_brave_api_token

# 其他选项
SEARCH_SERVICE=google  # 需要 GOOGLE_KEY 和 GOOGLE_CX
SEARCH_SERVICE=bing    # 需要 BING_KEY
```

### AI服务配置

```env
# OpenAI (推荐)
OPENAI_API_KEY=your_openai_api_key
OPENAI_MODEL=gpt-3.5-turbo  # 或 gpt-4
OPENAI_MAX_TOKENS=3000
OPENAI_TEMPERATURE=0.7

# 其他AI服务
CLAUDE_API_KEY=your_claude_api_key
GROQ_API_KEY=your_groq_api_key
MOONSHOT_API_KEY=your_moonshot_api_key
DEEPSEEK_API_KEY=your_deepseek_api_key
```

### 文章生成配置

```env
# 新闻搜索配置
NEWS_MAX_COUNT=10          # 最大新闻数量
MAX_RESULTS=15             # 搜索结果数量

# 文章风格配置
ARTICLE_STYLE=公众号        # 文章风格
ARTICLE_LENGTH=800-1200字   # 文章长度
```

## 🎯 使用示例

### 基础使用

```python
from news_article_generator import NewsArticleGenerator

# 初始化生成器
generator = NewsArticleGenerator()

# 生成文章
result = generator.generate_article("人工智能")

if result['success']:
    print(f"文章生成成功！")
    print(f"文章长度: {result['article_length']} 字符")
    print(f"新闻来源: {result['news_count']} 条")
    
    # 保存文章
    filepath = generator.save_article(result)
    print(f"文章已保存到: {filepath}")
else:
    print(f"生成失败: {result['error']}")
```

### 高级使用

```python
# 自定义配置
generator = NewsArticleGenerator()

# 1. 搜索新闻
news_list = generator.search_news("量子计算")

# 2. 整合内容
integrated_content = generator.integrate_news_content(news_list)

# 3. 生成提示词
prompt = generator.generate_article_prompt("量子计算", integrated_content)

# 4. 调用AI生成
article = generator.call_ai_service(prompt)

print(f"生成的文章:\n{article}")
```

## 📊 功能特性详解

### 智能搜索优化

- **关键词增强**: 自动为不同类型的关键词添加相关搜索词
- **相关性评分算法**:
  - 标题匹配: 50分 (完全匹配额外+20分)
  - 摘要匹配: 30分
  - 时间新鲜度: 最高10分
  - 来源可信度: 10分
  - 内容长度: 5分

### 内容整合策略

- **重点新闻** (评分≥70): 详细展示，包含完整摘要和链接
- **相关新闻** (评分40-69): 简化展示，突出关键信息
- **补充信息** (评分<40): 标题列表，提供参考

### AI提示词优化

- **结构化要求**: 明确的文章结构和长度要求
- **风格指导**: 公众号特色的语言风格和表达方式
- **技巧提示**: 写作技巧和互动元素建议
- **质量标准**: 原创性、观点性、可读性要求

## 🔧 故障排除

### 常见问题

1. **搜索失败**
   - 检查网络连接
   - 确认搜索服务配置
   - 验证API密钥有效性

2. **AI生成失败**
   - 检查AI服务API密钥
   - 确认API额度充足
   - 尝试降低max_tokens设置

3. **内容质量不佳**
   - 调整搜索关键词
   - 增加新闻数量设置
   - 优化AI模型参数

### 调试模式

```bash
# 运行测试脚本查看详细日志
python test_news_generator.py

# 检查环境配置
python -c "from news_article_generator import NewsArticleGenerator; NewsArticleGenerator()"
```

## 📈 性能优化

### 搜索优化
- 使用Brave搜索获得更好的新闻质量
- 调整MAX_RESULTS平衡速度和质量
- 启用CRAWL_RESULTS获取完整内容

### AI优化
- 使用GPT-4获得更高质量文章
- 调整temperature控制创意度
- 增加max_tokens支持更长文章

### 缓存优化
- 搜索结果可以缓存避免重复请求
- AI生成结果可以保存供后续参考

## 🤝 贡献指南

欢迎提交Issue和Pull Request来改进这个项目！

### 开发环境

```bash
# 克隆项目
git clone <repository-url>
cd S-searchapi

# 安装开发依赖
pip install -e ".[dev]"

# 运行测试
python test_news_generator.py
```

## 📄 许可证

MIT License - 详见LICENSE文件

## 🙏 致谢

- 感谢各搜索API服务提供商
- 感谢OpenAI、Anthropic等AI服务提供商
- 感谢开源社区的贡献

---

**💡 提示**: 首次使用建议先运行 `python demo_optimized.py` 体验完整功能！
