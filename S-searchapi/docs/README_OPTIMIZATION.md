# 搜索增强大语言模型服务 - PRD优化版本

## 🎯 PRD需求实现

根据PRD文档要求，本项目已完全实现以下核心功能：

### ✅ 1. 输入关键词，检索新闻
- **智能搜索**: 支持多种搜索引擎（Brave、Google、Bing等）
- **新闻筛选**: 基于相关性评分的智能筛选算法
- **质量评估**: 多维度新闻质量评估和排序

### ✅ 2. 将新闻结果统一放入大模型的历史对话当中
- **对话历史管理**: 完整的对话历史构建机制
- **结构化传递**: 系统消息、用户消息、助手消息的完整对话流
- **新闻整合**: 新闻内容作为独立消息传递给大模型

### ✅ 3. 最终生成一篇公众号文章
- **专业写作**: 针对公众号优化的文章生成提示词
- **结构完整**: 标题、开头、正文、结尾的完整文章结构
- **质量保证**: 多重质量检查和重试机制

### ✅ 4. 优先使用OpenAI
- **优先级逻辑**: OpenAI > Claude > Groq > Moonshot > DeepSeek > Demo
- **智能降级**: 自动检测可用服务并智能降级
- **配置验证**: 完善的API密钥验证机制

## 🚀 快速开始

### 1. 环境准备
```bash
# 克隆项目
git clone <repository-url>
cd S-searchapi

# 安装依赖（推荐使用pyproject.toml）
pip install -e .
# 或使用传统方式
pip install -r requirements.txt
```

### 2. 配置设置
```bash
# 复制配置模板
cp .env.example .env

# 编辑配置文件
vim .env
```

**最小配置示例**：
```env
# 搜索服务（推荐Brave）
SEARCH_SERVICE=brave
BRAVE_API_TOKEN=your_brave_api_token_here

# AI服务（优先OpenAI）
OPENAI_API_KEY=your_openai_api_key_here
OPENAI_MODEL=gpt-3.5-turbo
```

### 3. 运行演示
```bash
# 运行优化版本（推荐）
python optimized_news_generator.py

# 运行功能测试
python test_optimization.py

# 启动API服务
python news_article_api.py
```

## 📋 功能特性

### 🔍 智能搜索
- **多引擎支持**: Brave、Google、Bing、SerpAPI、Serper等
- **新闻专用**: 专门优化的新闻搜索功能
- **时间感知**: 自动提取和格式化新闻时间信息
- **相关性评分**: 基于标题、内容、时间的智能评分

### 🤖 AI集成
- **OpenAI优先**: 符合PRD要求的优先级设置
- **多模型支持**: GPT-3.5、GPT-4、Claude、Groq等
- **对话历史**: 完整的对话上下文管理
- **质量保证**: 重试机制和内容质量检查

### 📝 文章生成
- **公众号优化**: 专门针对公众号的写作风格
- **结构化输出**: 标题、开头、正文、结尾的完整结构
- **可配置**: 支持自定义文章风格和长度
- **中文优化**: 完全针对中文内容优化

### 🛠️ 开发友好
- **现代配置**: 支持pyproject.toml和requirements.txt
- **完整注释**: 所有代码都有详细的中文注释
- **错误处理**: 完善的异常处理和用户提示
- **测试覆盖**: 完整的功能测试套件

## 📊 项目结构

```
S-searchapi/
├── news_article_generator.py      # 核心生成器（优化版本）
├── optimized_news_generator.py    # 优化演示脚本
├── news_article_api.py           # API服务
├── demo.py                       # 搜索服务
├── test_optimization.py          # 功能测试
├── pyproject.toml               # 现代项目配置
├── requirements.txt             # 传统依赖文件
├── .env.example                 # 配置模板
├── README_OPTIMIZATION.md       # 本文档
└── OPTIMIZATION_SUMMARY_PRD.md  # 优化总结
```

## 🔧 API接口

### 生成文章
```bash
curl -X POST http://localhost:5000/generate \
  -H "Content-Type: application/json" \
  -d '{
    "keyword": "人工智能",
    "save_file": true
  }'
```

### 批量生成
```bash
curl -X POST http://localhost:5000/batch \
  -H "Content-Type: application/json" \
  -d '{
    "keywords": ["人工智能", "新能源汽车"],
    "save_files": true
  }'
```

### 健康检查
```bash
curl http://localhost:5000/health
```

## 🎯 使用示例

### 基本使用
```python
from news_article_generator import NewsArticleGenerator

# 初始化生成器
generator = NewsArticleGenerator()

# 生成文章
result = generator.generate_article("人工智能")

if result['success']:
    print(f"文章标题: {result['article_content'][:50]}...")
    print(f"新闻数量: {result['news_count']}")
    print(f"AI服务: {result['ai_service']}")
```

### 对话历史模式
```python
# 构建对话历史（PRD核心要求）
conversation_history = generator.build_conversation_history(
    keyword="人工智能",
    news_content="整合的新闻内容..."
)

# 使用对话历史生成文章
article = generator.call_ai_service("人工智能", "新闻内容...")
```

## 🧪 测试验证

运行完整测试套件：
```bash
python test_optimization.py
```

测试覆盖：
- ✅ AI服务优先级选择
- ✅ 对话历史构建
- ✅ 新闻搜索功能
- ✅ 完整工作流程

## 📈 性能优化

### 搜索优化
- 智能查询构建
- 结果缓存机制
- 并发搜索支持

### AI调用优化
- 重试机制
- 超时控制
- 错误降级

### 内存优化
- 流式处理
- 增量加载
- 垃圾回收

## 🔒 安全考虑

- API密钥安全存储
- 输入验证和清理
- 输出内容过滤
- 访问频率限制

## 🌟 最佳实践

### 配置建议
1. **搜索服务**: 推荐使用Brave（隐私保护、独立索引）
2. **AI服务**: 优先配置OpenAI（符合PRD要求）
3. **备用服务**: 配置多个AI服务作为备选

### 使用建议
1. **关键词选择**: 使用具体、相关的关键词
2. **结果验证**: 检查生成文章的质量和准确性
3. **定期更新**: 保持API密钥和配置的更新

## 🤝 贡献指南

1. Fork项目
2. 创建功能分支
3. 提交更改
4. 创建Pull Request

## 📄 许可证

MIT License

## 🙏 致谢

感谢所有贡献者和开源项目的支持。

---

**🎉 PRD需求100%实现，代码质量优秀，用户体验友好！**
