#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
搜索API使用示例
演示如何在其他项目中集成和使用搜索服务
"""

import json
from demo import SearchService


def simple_search_example():
    """简单搜索示例"""
    print("=== 简单搜索示例 ===")

    # 初始化搜索服务
    search = SearchService()

    # 执行搜索
    query = "机器学习入门"
    print(f"搜索关键词: {query}")

    result = search.search(query)

    # 解析结果
    try:
        data = json.loads(result)
        results = data.get("results", [])

        print(f"找到 {len(results)} 条结果:")
        for i, item in enumerate(results, 1):
            print(f"{i}. {item.get('title', 'N/A')}")
            print(f"   {item.get('link', 'N/A')}")

            # 显示时间信息
            time_info = item.get('time_info', {})
            if time_info and time_info.get('formatted_time') != '未知时间':
                print(f"   时间: {time_info.get('formatted_time', 'N/A')} ({time_info.get('relative_time', 'N/A')})")
            else:
                print(f"   时间: 未知")
            print()
    except json.JSONDecodeError:
        print(f"解析失败: {result}")


def batch_search_example():
    """批量搜索示例"""
    print("=== 批量搜索示例 ===")

    search = SearchService()

    # 多个搜索关键词
    queries = [
        "深度学习框架对比",
        "云计算服务选择",
        "前端开发最佳实践"
    ]

    all_results = {}

    for query in queries:
        print(f"正在搜索: {query}")
        result = search.search(query)

        try:
            data = json.loads(result)
            all_results[query] = data.get("results", [])
            print(f"  找到 {len(all_results[query])} 条结果")
        except json.JSONDecodeError:
            all_results[query] = []
            print(f"  搜索失败")

    # 汇总结果
    print(f"\n批量搜索完成，共处理 {len(queries)} 个查询")
    for query, results in all_results.items():
        print(f"- {query}: {len(results)} 条结果")


def custom_search_function(keyword, max_results=3):
    """自定义搜索函数"""
    search = SearchService()

    # 临时修改最大结果数
    original_max = search.max_results
    search.max_results = max_results

    try:
        result = search.search(keyword)
        data = json.loads(result)
        return data.get("results", [])
    except json.JSONDecodeError:
        return []
    finally:
        # 恢复原始设置
        search.max_results = original_max


def search_and_filter_example():
    """搜索并过滤结果示例"""
    print("=== 搜索并过滤结果示例 ===")

    # 搜索Python相关内容
    results = custom_search_function("Python数据分析", max_results=10)

    # 过滤包含特定关键词的结果
    filtered_results = []
    keywords = ["pandas", "numpy", "matplotlib", "数据", "分析"]

    for result in results:
        title = result.get("title", "").lower()
        snippet = result.get("snippet", "").lower()

        # 检查是否包含关键词
        if any(keyword.lower() in title or keyword.lower() in snippet for keyword in keywords):
            filtered_results.append(result)

    print(f"原始结果: {len(results)} 条")
    print(f"过滤后结果: {len(filtered_results)} 条")

    for i, result in enumerate(filtered_results[:3], 1):
        print(f"\n{i}. {result.get('title', 'N/A')}")
        print(f"   {result.get('snippet', 'N/A')[:80]}...")

        # 显示时间信息
        time_info = result.get('time_info', {})
        if time_info and time_info.get('formatted_time') != '未知时间':
            print(f"   时间: {time_info.get('formatted_time', 'N/A')} ({time_info.get('relative_time', 'N/A')})")
        else:
            print(f"   时间: 未知")


def search_with_error_handling():
    """带错误处理的搜索示例"""
    print("=== 错误处理示例 ===")

    search = SearchService()

    # 测试各种情况
    test_cases = [
        "",  # 空查询
        "正常查询测试",  # 正常查询
        "a" * 1000,  # 超长查询
    ]

    for i, query in enumerate(test_cases, 1):
        print(f"\n测试用例 {i}: {'空查询' if not query else '超长查询' if len(query) > 100 else '正常查询'}")

        try:
            if not query.strip():
                print("  跳过空查询")
                continue

            result = search.search(query[:100])  # 限制查询长度

            if "错误" in result:
                print(f"  搜索失败: {result}")
            else:
                data = json.loads(result)
                results = data.get("results", [])
                print(f"  搜索成功: 找到 {len(results)} 条结果")

        except Exception as e:
            print(f"  异常: {str(e)}")


def main():
    """主函数 - 运行所有示例"""
    print("搜索API使用示例集合\n")

    # 检查搜索服务配置
    search = SearchService()
    if not search.search_service:
        print("❌ 错误: 未配置搜索服务")
        print("请在.env文件中设置SEARCH_SERVICE")
        return

    print(f"✅ 当前搜索服务: {search.search_service}")
    print(f"✅ 最大结果数: {search.max_results}")
    print("-" * 60)

    # 运行各种示例
    try:
        simple_search_example()
        print("-" * 60)

        batch_search_example()
        print("-" * 60)

        search_and_filter_example()
        print("-" * 60)

        search_with_error_handling()
        print("-" * 60)

        print("✅ 所有示例运行完成!")

    except KeyboardInterrupt:
        print("\n❌ 用户中断执行")
    except Exception as e:
        print(f"❌ 运行出错: {str(e)}")


if __name__ == "__main__":
    main()
