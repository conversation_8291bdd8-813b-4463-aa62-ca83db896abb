#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
新闻文章生成器API接口
提供HTTP API服务，方便其他系统调用
"""

import os
import json
import time
from datetime import datetime
from typing import Dict, Any
from flask import Flask, request, jsonify, send_file
from dotenv import load_dotenv
from news_article_generator import NewsArticleGenerator

# 加载环境变量
load_dotenv()

# 创建Flask应用
app = Flask(__name__)

# 全局生成器实例
generator = None


def init_generator():
    """初始化生成器"""
    global generator
    if generator is None:
        generator = NewsArticleGenerator()
    return generator


@app.route('/health', methods=['GET'])
def health_check():
    """健康检查接口"""
    return jsonify({
        'status': 'healthy',
        'timestamp': datetime.now().isoformat(),
        'service': 'news-article-generator'
    })


@app.route('/config', methods=['GET'])
def get_config():
    """获取当前配置信息"""
    gen = init_generator()
    
    return jsonify({
        'status': 'success',
        'config': {
            'search_service': gen.search_service.search_service if hasattr(gen.search_service, 'search_service') else 'unknown',
            'ai_service': gen.ai_service,
            'max_news_count': gen.max_news_count,
            'article_style': gen.article_style,
            'article_length': gen.article_length
        },
        'timestamp': datetime.now().isoformat()
    })


@app.route('/generate', methods=['POST'])
def generate_article():
    """
    生成新闻文章接口
    
    请求体:
    {
        "keyword": "搜索关键词",
        "save_file": true/false (可选，是否保存文件)
    }
    
    响应:
    {
        "status": "success/error",
        "data": {
            "keyword": "关键词",
            "article": "文章内容",
            "metadata": {...}
        },
        "error": "错误信息"
    }
    """
    try:
        # 解析请求参数
        data = request.get_json()
        if not data:
            return jsonify({
                'status': 'error',
                'error': '请求体不能为空',
                'timestamp': datetime.now().isoformat()
            }), 400
        
        keyword = data.get('keyword', '').strip()
        if not keyword:
            return jsonify({
                'status': 'error',
                'error': '关键词不能为空',
                'timestamp': datetime.now().isoformat()
            }), 400
        
        save_file = data.get('save_file', False)
        
        # 初始化生成器
        gen = init_generator()
        
        # 生成文章
        result = gen.generate_article(keyword)
        
        # 构建响应
        if result['success']:
            response_data = {
                'status': 'success',
                'data': {
                    'keyword': result['keyword'],
                    'article': result['article_content'],
                    'metadata': {
                        'news_count': result['news_count'],
                        'article_length': result['article_length'],
                        'ai_service': result['ai_service'],
                        'elapsed_time': result['elapsed_time'],
                        'timestamp': result['timestamp']
                    }
                },
                'timestamp': datetime.now().isoformat()
            }
            
            # 保存文件（如果需要）
            if save_file:
                filepath = gen.save_article(result, output_dir="api_output")
                response_data['data']['file_path'] = filepath
            
            return jsonify(response_data)
        else:
            return jsonify({
                'status': 'error',
                'error': result['error'],
                'keyword': result['keyword'],
                'timestamp': datetime.now().isoformat()
            }), 500
            
    except Exception as e:
        return jsonify({
            'status': 'error',
            'error': f'服务器内部错误: {str(e)}',
            'timestamp': datetime.now().isoformat()
        }), 500


@app.route('/batch', methods=['POST'])
def batch_generate():
    """
    批量生成文章接口
    
    请求体:
    {
        "keywords": ["关键词1", "关键词2", ...],
        "save_files": true/false (可选)
    }
    """
    try:
        data = request.get_json()
        if not data:
            return jsonify({
                'status': 'error',
                'error': '请求体不能为空'
            }), 400
        
        keywords = data.get('keywords', [])
        if not keywords or not isinstance(keywords, list):
            return jsonify({
                'status': 'error',
                'error': '关键词列表不能为空'
            }), 400
        
        save_files = data.get('save_files', False)
        
        # 限制批量数量
        max_batch_size = int(os.getenv('MAX_BATCH_SIZE', '10'))
        if len(keywords) > max_batch_size:
            return jsonify({
                'status': 'error',
                'error': f'批量数量不能超过 {max_batch_size}'
            }), 400
        
        # 初始化生成器
        gen = init_generator()
        
        # 批量生成
        results = []
        for keyword in keywords:
            keyword = str(keyword).strip()
            if not keyword:
                continue
                
            result = gen.generate_article(keyword)
            
            if result['success']:
                item = {
                    'keyword': result['keyword'],
                    'article': result['article_content'],
                    'metadata': {
                        'news_count': result['news_count'],
                        'article_length': result['article_length'],
                        'ai_service': result['ai_service'],
                        'elapsed_time': result['elapsed_time']
                    }
                }
                
                # 保存文件（如果需要）
                if save_files:
                    filepath = gen.save_article(result, output_dir="batch_api_output")
                    item['file_path'] = filepath
                
                results.append(item)
            else:
                results.append({
                    'keyword': keyword,
                    'error': result['error']
                })
        
        # 统计结果
        success_count = sum(1 for r in results if 'article' in r)
        
        return jsonify({
            'status': 'success',
            'data': {
                'results': results,
                'summary': {
                    'total': len(results),
                    'success': success_count,
                    'failed': len(results) - success_count
                }
            },
            'timestamp': datetime.now().isoformat()
        })
        
    except Exception as e:
        return jsonify({
            'status': 'error',
            'error': f'服务器内部错误: {str(e)}',
            'timestamp': datetime.now().isoformat()
        }), 500


@app.route('/download/<filename>', methods=['GET'])
def download_file(filename):
    """下载生成的文章文件"""
    try:
        # 安全检查文件名
        if '..' in filename or '/' in filename:
            return jsonify({
                'status': 'error',
                'error': '无效的文件名'
            }), 400
        
        # 查找文件
        possible_dirs = ['output', 'api_output', 'batch_api_output']
        filepath = None
        
        for dir_name in possible_dirs:
            test_path = os.path.join(dir_name, filename)
            if os.path.exists(test_path):
                filepath = test_path
                break
        
        if not filepath:
            return jsonify({
                'status': 'error',
                'error': '文件不存在'
            }), 404
        
        return send_file(filepath, as_attachment=True)
        
    except Exception as e:
        return jsonify({
            'status': 'error',
            'error': f'下载失败: {str(e)}'
        }), 500


@app.errorhandler(404)
def not_found(error):
    """404错误处理"""
    return jsonify({
        'status': 'error',
        'error': '接口不存在',
        'timestamp': datetime.now().isoformat()
    }), 404


@app.errorhandler(500)
def internal_error(error):
    """500错误处理"""
    return jsonify({
        'status': 'error',
        'error': '服务器内部错误',
        'timestamp': datetime.now().isoformat()
    }), 500


def main():
    """启动API服务"""
    print("🚀 启动新闻文章生成器API服务")
    print("=" * 50)
    
    # 初始化生成器
    gen = init_generator()
    print(f"✅ 生成器初始化完成")
    print(f"   搜索服务: {gen.search_service.search_service if hasattr(gen.search_service, 'search_service') else 'unknown'}")
    print(f"   AI服务: {gen.ai_service}")
    
    # 配置参数
    host = os.getenv('API_HOST', '0.0.0.0')
    port = int(os.getenv('API_PORT', '5000'))
    debug = os.getenv('API_DEBUG', 'false').lower() == 'true'
    
    print(f"\n🌐 API服务配置:")
    print(f"   地址: http://{host}:{port}")
    print(f"   调试模式: {debug}")
    
    print(f"\n📋 可用接口:")
    print(f"   GET  /health          - 健康检查")
    print(f"   GET  /config          - 获取配置")
    print(f"   POST /generate        - 生成文章")
    print(f"   POST /batch           - 批量生成")
    print(f"   GET  /download/<file> - 下载文件")
    
    print(f"\n🔄 启动服务...")
    
    try:
        app.run(host=host, port=port, debug=debug)
    except KeyboardInterrupt:
        print(f"\n👋 服务已停止")
    except Exception as e:
        print(f"\n❌ 服务启动失败: {str(e)}")


if __name__ == "__main__":
    main()
