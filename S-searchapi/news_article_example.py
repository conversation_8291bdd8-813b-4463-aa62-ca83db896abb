#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
新闻文章生成器使用示例
演示如何使用NewsArticleGenerator生成公众号文章
"""

import os
import json
from dotenv import load_dotenv
from news_article_generator import NewsArticleGenerator

# 加载环境变量
load_dotenv()


def example_basic_usage():
    """基础使用示例"""
    print("📰 基础使用示例")
    print("=" * 50)
    
    # 初始化生成器
    generator = NewsArticleGenerator()
    
    # 生成文章
    keyword = "人工智能"
    result = generator.generate_article(keyword)
    
    # 检查结果
    if result['success']:
        print(f"✅ 文章生成成功!")
        print(f"   关键词: {result['keyword']}")
        print(f"   新闻数量: {result['news_count']}")
        print(f"   文章长度: {result['article_length']} 字符")
        print(f"   耗时: {result['elapsed_time']} 秒")
        
        # 保存文章
        filepath = generator.save_article(result)
        if filepath:
            print(f"   保存路径: {filepath}")
    else:
        print(f"❌ 文章生成失败: {result['error']}")


def example_batch_generation():
    """批量生成示例"""
    print("\n📰 批量生成示例")
    print("=" * 50)
    
    # 初始化生成器
    generator = NewsArticleGenerator()
    
    # 批量关键词
    keywords = [
        "新能源汽车",
        "区块链技术", 
        "元宇宙",
        "量子计算",
        "5G技术"
    ]
    
    results = []
    
    for i, keyword in enumerate(keywords, 1):
        print(f"\n🔄 正在生成第 {i}/{len(keywords)} 篇文章: '{keyword}'")
        
        result = generator.generate_article(keyword)
        results.append(result)
        
        if result['success']:
            print(f"   ✅ 成功 - 长度: {result['article_length']} 字符")
            # 保存文章
            generator.save_article(result, output_dir="batch_output")
        else:
            print(f"   ❌ 失败: {result['error']}")
    
    # 统计结果
    success_count = sum(1 for r in results if r['success'])
    print(f"\n📊 批量生成完成:")
    print(f"   总数: {len(results)}")
    print(f"   成功: {success_count}")
    print(f"   失败: {len(results) - success_count}")


def example_custom_config():
    """自定义配置示例"""
    print("\n📰 自定义配置示例")
    print("=" * 50)
    
    # 临时修改环境变量
    original_max_count = os.getenv('NEWS_MAX_COUNT')
    original_style = os.getenv('ARTICLE_STYLE')
    original_length = os.getenv('ARTICLE_LENGTH')
    
    # 设置自定义配置
    os.environ['NEWS_MAX_COUNT'] = '5'  # 只使用5条新闻
    os.environ['ARTICLE_STYLE'] = '科技博客'  # 改为科技博客风格
    os.environ['ARTICLE_LENGTH'] = '500-800字'  # 较短的文章
    
    try:
        # 初始化生成器（会读取新的配置）
        generator = NewsArticleGenerator()
        
        # 生成文章
        result = generator.generate_article("机器学习")
        
        if result['success']:
            print(f"✅ 自定义配置文章生成成功!")
            print(f"   使用新闻数: {result['news_count']}")
            print(f"   文章风格: {result['metadata']['style']}")
            print(f"   目标长度: {result['metadata']['target_length']}")
            
            # 保存到自定义目录
            generator.save_article(result, output_dir="custom_output")
        else:
            print(f"❌ 生成失败: {result['error']}")
    
    finally:
        # 恢复原始配置
        if original_max_count:
            os.environ['NEWS_MAX_COUNT'] = original_max_count
        if original_style:
            os.environ['ARTICLE_STYLE'] = original_style
        if original_length:
            os.environ['ARTICLE_LENGTH'] = original_length


def example_api_integration():
    """API集成示例"""
    print("\n📰 API集成示例")
    print("=" * 50)
    
    def generate_article_api(keyword: str) -> dict:
        """
        模拟API接口
        
        Args:
            keyword: 搜索关键词
            
        Returns:
            API响应格式的结果
        """
        generator = NewsArticleGenerator()
        result = generator.generate_article(keyword)
        
        # 转换为API响应格式
        if result['success']:
            api_response = {
                "status": "success",
                "data": {
                    "keyword": result['keyword'],
                    "article": result['article_content'],
                    "metadata": {
                        "news_count": result['news_count'],
                        "article_length": result['article_length'],
                        "ai_service": result['ai_service'],
                        "elapsed_time": result['elapsed_time'],
                        "timestamp": result['timestamp']
                    }
                }
            }
        else:
            api_response = {
                "status": "error",
                "error": {
                    "message": result['error'],
                    "keyword": result['keyword'],
                    "timestamp": result['timestamp']
                }
            }
        
        return api_response
    
    # 测试API接口
    keyword = "云计算"
    print(f"🔄 调用API生成文章: '{keyword}'")
    
    api_result = generate_article_api(keyword)
    
    print(f"📤 API响应:")
    print(json.dumps(api_result, ensure_ascii=False, indent=2))


def example_error_handling():
    """错误处理示例"""
    print("\n📰 错误处理示例")
    print("=" * 50)
    
    generator = NewsArticleGenerator()
    
    # 测试各种错误情况
    test_cases = [
        "",  # 空关键词
        "   ",  # 空白关键词
        "a" * 1000,  # 超长关键词
        "正常关键词测试",  # 正常关键词
    ]
    
    for i, keyword in enumerate(test_cases, 1):
        print(f"\n🧪 测试用例 {i}: {'空关键词' if not keyword.strip() else '超长关键词' if len(keyword) > 100 else '正常关键词'}")
        
        try:
            if not keyword.strip():
                print("   ⚠️ 跳过空关键词")
                continue
            
            # 限制关键词长度
            test_keyword = keyword[:50] if len(keyword) > 50 else keyword
            
            result = generator.generate_article(test_keyword)
            
            if result['success']:
                print(f"   ✅ 成功: 文章长度 {result['article_length']} 字符")
            else:
                print(f"   ❌ 失败: {result['error']}")
                
        except Exception as e:
            print(f"   💥 异常: {str(e)}")


def main():
    """主函数 - 运行所有示例"""
    print("🚀 新闻文章生成器使用示例集合")
    print("=" * 60)
    
    # 检查配置
    generator = NewsArticleGenerator()
    print(f"✅ 当前配置:")
    print(f"   搜索服务: {generator.search_service.search_service}")
    print(f"   AI服务: {generator.ai_service}")
    print(f"   最大新闻数: {generator.max_news_count}")
    
    try:
        # 运行各种示例
        example_basic_usage()
        
        # 询问是否继续运行其他示例
        if input("\n是否继续运行批量生成示例? (y/n): ").lower() == 'y':
            example_batch_generation()
        
        if input("\n是否继续运行自定义配置示例? (y/n): ").lower() == 'y':
            example_custom_config()
        
        if input("\n是否继续运行API集成示例? (y/n): ").lower() == 'y':
            example_api_integration()
        
        if input("\n是否继续运行错误处理示例? (y/n): ").lower() == 'y':
            example_error_handling()
        
        print(f"\n🎉 所有示例运行完成!")
        
    except KeyboardInterrupt:
        print(f"\n\n👋 用户中断，程序退出")
    except Exception as e:
        print(f"\n❌ 程序出错: {str(e)}")


if __name__ == "__main__":
    main()
