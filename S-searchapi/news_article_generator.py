#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
新闻文章生成器
根据关键词搜索最新新闻，并使用AI生成公众号文章

业务流程：
1. 输入关键词
2. 根据关键词，检索最新新闻消息
3. 获取得到新闻消息整合一起
4. 发送给AI大模型，生成一篇公众号文章
"""

import os
import json
import time
from datetime import datetime
from typing import List, Dict, Optional
from dotenv import load_dotenv

# 导入现有的搜索服务
from demo import SearchService

# 加载环境变量
load_dotenv()


class NewsArticleGenerator:
    """新闻文章生成器"""

    def __init__(self):
        """初始化生成器"""
        self.search_service = SearchService()
        self.ai_service = self._get_ai_service()

        # 配置参数
        self.max_news_count = int(os.getenv('NEWS_MAX_COUNT', '10'))
        self.article_style = os.getenv('ARTICLE_STYLE', '公众号')
        self.article_length = os.getenv('ARTICLE_LENGTH', '800-1200字')

        print(f"📰 新闻文章生成器初始化完成")
        print(f"   搜索服务: {self.search_service.search_service}")
        print(f"   AI服务: {self.ai_service}")
        print(f"   最大新闻数: {self.max_news_count}")
        print(f"   文章风格: {self.article_style}")
        print(f"   文章长度: {self.article_length}")

    def _get_ai_service(self) -> str:
        """
        获取AI服务配置
        优先使用OpenAI，符合PRD要求
        """
        # 优先检查OpenAI配置（PRD要求）
        openai_key = os.getenv('OPENAI_API_KEY')
        if openai_key and openai_key.strip() and openai_key != 'your_openai_api_key_here':
            return 'openai'

        # 备选AI服务
        claude_key = os.getenv('CLAUDE_API_KEY')
        if claude_key and claude_key.strip() and claude_key != 'your_claude_api_key_here':
            return 'claude'

        groq_key = os.getenv('GROQ_API_KEY')
        if groq_key and groq_key.strip() and groq_key != 'your_groq_api_key_here':
            return 'groq'

        moonshot_key = os.getenv('MOONSHOT_API_KEY')
        if moonshot_key and moonshot_key.strip() and moonshot_key != 'your_moonshot_api_key_here':
            return 'moonshot'

        deepseek_key = os.getenv('DEEPSEEK_API_KEY')
        if deepseek_key and deepseek_key.strip() and deepseek_key != 'your_deepseek_api_key_here':
            return 'deepseek'

        # 如果没有配置任何AI服务，使用演示模式
        print("⚠️ 未检测到有效的AI服务配置，将使用演示模式")
        print("💡 建议配置OpenAI API密钥以获得最佳体验")
        return 'demo'

    def search_news(self, keyword: str) -> List[Dict]:
        """
        搜索新闻

        Args:
            keyword: 搜索关键词

        Returns:
            新闻列表
        """
        print(f"\n🔍 开始搜索新闻: '{keyword}'")

        try:
            # 构建更精确的搜索查询
            search_query = self._build_search_query(keyword)
            print(f"   搜索查询: '{search_query}'")

            # 使用搜索服务获取新闻
            result = self.search_service.search(search_query)

            # 解析搜索结果
            if isinstance(result, str):
                try:
                    data = json.loads(result)
                    news_list = data.get('results', [])
                except json.JSONDecodeError:
                    print(f"❌ 搜索结果解析失败: {result}")
                    return []
            else:
                news_list = result.get('results', []) if isinstance(result, dict) else []

            print(f"📊 原始搜索结果: {len(news_list)} 条")

            # 过滤和排序新闻
            filtered_news = self._filter_and_sort_news(news_list, keyword)

            # 限制新闻数量
            filtered_news = filtered_news[:self.max_news_count]

            print(f"✅ 筛选后新闻: {len(filtered_news)} 条")

            # 格式化新闻数据
            formatted_news = []
            for i, news in enumerate(filtered_news, 1):
                formatted_item = {
                    'index': i,
                    'title': news.get('title', ''),
                    'link': news.get('link', ''),
                    'snippet': news.get('snippet', ''),
                    'time_info': news.get('time_info', {}),
                    'source': news.get('source', ''),
                    'relevance_score': news.get('relevance_score', 0),  # 相关性评分
                }
                formatted_news.append(formatted_item)

                # 显示新闻信息
                time_str = formatted_item['time_info'].get('formatted_time', '未知时间')
                print(f"   {i}. {formatted_item['title'][:50]}... ({time_str})")

            return formatted_news

        except Exception as e:
            print(f"❌ 搜索新闻时出错: {str(e)}")
            return []

    def _build_search_query(self, keyword: str) -> str:
        """
        构建搜索查询

        Args:
            keyword: 原始关键词

        Returns:
            优化后的搜索查询
        """
        # 添加时间限制和相关性关键词
        current_year = datetime.now().year

        # 根据关键词类型添加相关词汇
        tech_keywords = ['AI', '人工智能', '机器学习', '深度学习', '算法', '技术', '科技', '创新']
        business_keywords = ['公司', '企业', '商业', '市场', '投资', '融资', '上市', '财报']
        policy_keywords = ['政策', '法规', '监管', '政府', '国家', '部门', '规定', '标准']

        enhanced_query = keyword

        # 根据关键词添加相关搜索词
        if any(tech_word in keyword for tech_word in tech_keywords):
            enhanced_query += " 最新 发展 突破"
        elif any(biz_word in keyword for biz_word in business_keywords):
            enhanced_query += " 最新 动态 消息"
        elif any(policy_word in keyword for policy_word in policy_keywords):
            enhanced_query += " 最新 发布 通知"
        else:
            enhanced_query += " 最新 新闻"

        return enhanced_query

    def _filter_and_sort_news(self, news_list: List[Dict], keyword: str) -> List[Dict]:
        """
        过滤和排序新闻

        Args:
            news_list: 原始新闻列表
            keyword: 搜索关键词

        Returns:
            过滤排序后的新闻列表
        """
        if not news_list:
            return []

        # 为每条新闻计算相关性评分
        scored_news = []
        for news in news_list:
            score = self._calculate_relevance_score(news, keyword)
            if score > 0:  # 只保留有相关性的新闻
                news['relevance_score'] = score
                scored_news.append(news)

        # 按相关性评分和时间排序
        scored_news.sort(key=lambda x: (x['relevance_score'], self._get_time_score(x)), reverse=True)

        print(f"📈 新闻质量评分完成，保留 {len(scored_news)} 条高质量新闻")

        return scored_news

    def _calculate_relevance_score(self, news: Dict, keyword: str) -> float:
        """
        计算新闻相关性评分

        Args:
            news: 新闻项
            keyword: 关键词

        Returns:
            相关性评分 (0-100)
        """
        score = 0.0
        title = news.get('title', '').lower()
        snippet = news.get('snippet', '').lower()
        keyword_lower = keyword.lower()

        # 标题匹配 (权重最高)
        if keyword_lower in title:
            score += 50
            # 完全匹配额外加分
            if title.find(keyword_lower) != -1:
                score += 20

        # 摘要匹配
        if keyword_lower in snippet:
            score += 30

        # 时间新鲜度加分
        time_score = self._get_time_score(news)
        score += time_score * 0.2

        # 来源可信度加分
        source = news.get('source', '').lower()
        trusted_sources = ['新华网', '人民网', '央视网', '中国新闻网', '澎湃新闻', '财新网', '36氪', '虎嗅网']
        if any(trusted in source for trusted in trusted_sources):
            score += 10

        # 内容长度加分 (适中的摘要长度更好)
        snippet_length = len(snippet)
        if 50 <= snippet_length <= 300:
            score += 5

        return min(score, 100)  # 最高100分

    def _get_time_score(self, news: Dict) -> float:
        """
        获取时间新鲜度评分

        Args:
            news: 新闻项

        Returns:
            时间评分 (0-10)
        """
        time_info = news.get('time_info', {})
        if not time_info:
            return 0

        try:
            # 这里可以根据时间信息计算新鲜度
            # 暂时返回固定值，实际应该根据发布时间计算
            relative_time = time_info.get('relative_time', '')
            if '小时' in relative_time or '分钟' in relative_time:
                return 10  # 最新
            elif '天' in relative_time:
                return 7   # 较新
            elif '周' in relative_time:
                return 5   # 一般
            else:
                return 2   # 较旧
        except:
            return 0

    def integrate_news_content(self, news_list: List[Dict]) -> str:
        """
        整合新闻内容

        Args:
            news_list: 新闻列表

        Returns:
            整合后的新闻内容
        """
        print(f"\n📝 开始整合新闻内容...")

        if not news_list:
            return "未找到相关新闻内容。"

        # 构建整合内容
        integrated_content = []

        # 添加整合概述
        total_news = len(news_list)
        avg_score = sum(news.get('relevance_score', 0) for news in news_list) / total_news if total_news > 0 else 0

        integrated_content.append("=== 📰 新闻内容整合报告 ===")
        integrated_content.append(f"📊 统计信息：共 {total_news} 条新闻，平均相关性评分 {avg_score:.1f}/100")
        integrated_content.append(f"⏰ 整合时间：{datetime.now().strftime('%Y年%m月%d日 %H:%M:%S')}")
        integrated_content.append("")

        # 按重要性分组新闻
        high_priority = [news for news in news_list if news.get('relevance_score', 0) >= 70]
        medium_priority = [news for news in news_list if 40 <= news.get('relevance_score', 0) < 70]
        low_priority = [news for news in news_list if news.get('relevance_score', 0) < 40]

        # 添加高优先级新闻
        if high_priority:
            integrated_content.append("🔥 【重点新闻】")
            for news in high_priority:
                content_block = self._format_news_item(news, detailed=True)
                integrated_content.append(content_block)
                integrated_content.append("")

        # 添加中等优先级新闻
        if medium_priority:
            integrated_content.append("📢 【相关新闻】")
            for news in medium_priority:
                content_block = self._format_news_item(news, detailed=False)
                integrated_content.append(content_block)
                integrated_content.append("")

        # 添加低优先级新闻（简化显示）
        if low_priority:
            integrated_content.append("📋 【补充信息】")
            for news in low_priority:
                content_block = self._format_news_item(news, detailed=False, simplified=True)
                integrated_content.append(content_block)

        # 添加内容分析
        integrated_content.append("")
        integrated_content.append("📈 【内容分析】")
        analysis = self._analyze_news_content(news_list)
        integrated_content.append(analysis)

        result = '\n'.join(integrated_content)

        print(f"✅ 新闻内容整合完成")
        print(f"   总长度: {len(result)} 字符")
        print(f"   重点新闻: {len(high_priority)} 条")
        print(f"   相关新闻: {len(medium_priority)} 条")
        print(f"   补充信息: {len(low_priority)} 条")

        return result

    def _format_news_item(self, news: Dict, detailed: bool = True, simplified: bool = False) -> str:
        """
        格式化单条新闻

        Args:
            news: 新闻项
            detailed: 是否显示详细信息
            simplified: 是否简化显示

        Returns:
            格式化后的新闻内容
        """
        if simplified:
            # 简化显示模式
            return f"• {news['title']} (评分: {news.get('relevance_score', 0):.0f})"

        content_parts = []

        # 标题和评分
        score = news.get('relevance_score', 0)
        score_emoji = "🔥" if score >= 70 else "📢" if score >= 40 else "📋"
        content_parts.append(f"{score_emoji} 【{news['title']}】(相关性: {score:.0f}/100)")

        if detailed:
            # 详细模式显示更多信息
            if news.get('snippet'):
                content_parts.append(f"📄 内容摘要：{news['snippet']}")

            # 时间和来源信息
            info_line = []
            time_info = news.get('time_info', {})
            if time_info and time_info.get('formatted_time', '未知时间') != '未知时间':
                info_line.append(f"⏰ {time_info.get('formatted_time', '未知')}")

            if news.get('source'):
                info_line.append(f"📰 {news['source']}")

            if info_line:
                content_parts.append(" | ".join(info_line))

            if news.get('link'):
                content_parts.append(f"🔗 链接：{news['link']}")
        else:
            # 简化模式只显示基本信息
            if news.get('snippet'):
                # 截取摘要前100字符
                snippet = news['snippet'][:100] + "..." if len(news['snippet']) > 100 else news['snippet']
                content_parts.append(f"📄 {snippet}")

        return '\n'.join(content_parts)

    def _analyze_news_content(self, news_list: List[Dict]) -> str:
        """
        分析新闻内容

        Args:
            news_list: 新闻列表

        Returns:
            分析结果
        """
        if not news_list:
            return "无新闻内容可分析。"

        analysis_parts = []

        # 时间分布分析
        time_distribution = {}
        for news in news_list:
            time_info = news.get('time_info', {})
            relative_time = time_info.get('relative_time', '未知')
            if '小时' in relative_time or '分钟' in relative_time:
                time_distribution['今日'] = time_distribution.get('今日', 0) + 1
            elif '天' in relative_time:
                time_distribution['近期'] = time_distribution.get('近期', 0) + 1
            else:
                time_distribution['较早'] = time_distribution.get('较早', 0) + 1

        if time_distribution:
            time_desc = []
            for period, count in time_distribution.items():
                time_desc.append(f"{period} {count} 条")
            analysis_parts.append(f"⏰ 时间分布：{', '.join(time_desc)}")

        # 来源分析
        sources = {}
        for news in news_list:
            source = news.get('source', '未知来源')
            sources[source] = sources.get(source, 0) + 1

        if sources:
            top_sources = sorted(sources.items(), key=lambda x: x[1], reverse=True)[:3]
            source_desc = [f"{source}({count}条)" for source, count in top_sources]
            analysis_parts.append(f"📰 主要来源：{', '.join(source_desc)}")

        # 质量分析
        high_quality = len([news for news in news_list if news.get('relevance_score', 0) >= 70])
        medium_quality = len([news for news in news_list if 40 <= news.get('relevance_score', 0) < 70])

        analysis_parts.append(f"📊 质量分布：高质量 {high_quality} 条，中等质量 {medium_quality} 条")

        return '\n'.join(analysis_parts)

    def generate_article_prompt(self, keyword: str, news_content: str) -> str:
        """
        生成文章创作提示词

        Args:
            keyword: 搜索关键词
            news_content: 整合的新闻内容

        Returns:
            AI提示词
        """
        prompt = f"""你是一位资深的公众号文章写手，擅长创作引人入胜、有深度的原创文章。请根据以下新闻素材，创作一篇关于"{keyword}"的高质量公众号文章。

## 📝 写作要求：
1. **文章风格**: {self.article_style}风格，语言生动活泼，富有感染力
2. **文章长度**: {self.article_length}，内容充实有料
3. **结构完整**: 标题吸睛、开头抓人、正文有料、结尾有力
4. **内容原创**: 深度解读新闻，提供独特视角和见解
5. **观点鲜明**: 有明确的态度和立场，但保持客观理性
6. **语言规范**: 使用地道中文，符合公众号阅读习惯

## 🎯 文章结构要求：
### 1. 标题设计 (15-25字)
- 使用数字、疑问句或感叹句增强吸引力
- 可以使用"重磅！"、"突发！"、"深度解读"等前缀
- 体现文章核心价值和看点

### 2. 开头段落 (100-200字)
- 用热点事件、数据或故事开场
- 快速抓住读者注意力
- 明确文章要解决的问题或提供的价值

### 3. 正文内容 (600-900字)
**第一部分：事件梳理** (200-300字)
- 客观描述最新发展动态
- 梳理事件时间线和关键节点
- 突出重要信息和数据

**第二部分：深度分析** (250-350字)
- 分析事件背后的原因和逻辑
- 探讨可能的影响和意义
- 提供专业见解和独特观点

**第三部分：趋势展望** (150-250字)
- 预测未来发展趋势
- 分析对相关行业/领域的影响
- 给出建设性建议或思考

### 4. 结尾段落 (100-150字)
- 总结全文核心观点
- 引发读者思考或讨论
- 可以提出问题或呼吁行动

## 📰 新闻素材：
{news_content}

## 🎨 写作技巧提示：
1. **多用短句**：提高阅读节奏感
2. **适当使用emoji**：增加趣味性（但不要过度）
3. **数据说话**：用具体数字增强说服力
4. **举例说明**：用生动案例帮助理解
5. **设置悬念**：在段落间制造阅读期待
6. **互动元素**：适当提出问题引发思考

## 🚀 创作主题：
请围绕"{keyword}"这个核心主题，结合上述新闻素材，创作一篇既有深度又有温度的公众号文章。文章要能让读者在3-5分钟内获得有价值的信息和启发。

请直接输出完整的文章内容，包括标题："""

        return prompt

    def build_conversation_history(self, keyword: str, news_content: str) -> List[Dict]:
        """
        构建对话历史，将新闻结果统一放入大模型的历史对话当中
        这是PRD的核心要求：将新闻结果统一放入大模型的历史对话当中

        Args:
            keyword: 搜索关键词
            news_content: 整合的新闻内容

        Returns:
            对话历史列表
        """
        print(f"\n💬 构建对话历史...")

        # 系统提示词 - 定义AI助手的角色和任务
        system_message = {
            "role": "system",
            "content": f"""你是一位资深的公众号文章写手，具有以下专业能力：
1. 深度理解热点事件，能够提供独特视角和专业分析
2. 擅长用生动有趣的语言讲述复杂话题
3. 精通公众号文章的写作技巧和传播规律
4. 能够创作既有深度又有温度的原创内容
5. 善于运用数据、案例和故事来增强文章说服力

你的任务是根据提供的新闻素材，创作一篇关于"{keyword}"的高质量公众号文章。
文章风格：{self.article_style}
文章长度：{self.article_length}

请严格按照要求的格式和风格创作文章。"""
        }

        # 用户请求 - 明确任务需求
        user_request = {
            "role": "user",
            "content": f"""请根据以下关于"{keyword}"的最新新闻素材，创作一篇高质量的公众号文章。

## 📝 写作要求：
1. **文章风格**: {self.article_style}风格，语言生动活泼，富有感染力
2. **文章长度**: {self.article_length}，内容充实有料
3. **结构完整**: 标题吸睛、开头抓人、正文有料、结尾有力
4. **内容原创**: 深度解读新闻，提供独特视角和见解
5. **观点鲜明**: 有明确的态度和立场，但保持客观理性

## 🎯 文章结构要求：
### 1. 标题设计 (15-25字)
- 使用数字、疑问句或感叹句增强吸引力
- 可以使用"重磅！"、"突发！"、"深度解读"等前缀

### 2. 开头段落 (100-200字)
- 用热点事件、数据或故事开场
- 快速抓住读者注意力

### 3. 正文内容 (600-900字)
- 事件梳理：客观描述最新发展动态
- 深度分析：分析事件背后的原因和逻辑
- 趋势展望：预测未来发展趋势

### 4. 结尾段落 (100-150字)
- 总结全文核心观点
- 引发读者思考或讨论

请直接输出完整的文章内容，包括标题。"""
        }

        # 助手回复 - 确认理解任务
        assistant_acknowledgment = {
            "role": "assistant",
            "content": f"""我理解了您的要求。我将根据提供的关于"{keyword}"的新闻素材，创作一篇{self.article_style}风格的公众号文章，长度控制在{self.article_length}。

我会确保文章具有：
- 吸引人的标题和开头
- 清晰的结构和逻辑
- 深度的分析和独特的见解
- 生动的语言和恰当的情感表达

现在请提供新闻素材，我将开始创作。"""
        }

        # 用户提供新闻素材 - 这是核心的新闻内容
        news_material = {
            "role": "user",
            "content": f"""以下是关于"{keyword}"的最新新闻素材：

{news_content}

请基于这些新闻素材创作文章。"""
        }

        # 构建完整的对话历史
        conversation_history = [
            system_message,
            user_request,
            assistant_acknowledgment,
            news_material
        ]

        print(f"✅ 对话历史构建完成")
        print(f"   消息数量: {len(conversation_history)}")
        print(f"   系统消息: 1条")
        print(f"   用户消息: 2条")
        print(f"   助手消息: 1条")
        print(f"   新闻素材长度: {len(news_content)} 字符")

        return conversation_history

    def call_ai_service(self, keyword: str, news_content: str) -> str:
        """
        调用AI服务生成文章
        使用对话历史的方式，符合PRD要求

        Args:
            keyword: 搜索关键词
            news_content: 整合的新闻内容

        Returns:
            生成的文章内容
        """
        print(f"\n🤖 开始调用AI服务生成文章...")
        print(f"   AI服务: {self.ai_service}")

        try:
            # 构建对话历史（PRD核心要求）
            conversation_history = self.build_conversation_history(keyword, news_content)

            if self.ai_service == 'openai':
                return self._call_openai_with_history(conversation_history)
            elif self.ai_service == 'claude':
                return self._call_claude_with_history(conversation_history)
            elif self.ai_service == 'groq':
                return self._call_groq_with_history(conversation_history)
            elif self.ai_service == 'moonshot':
                return self._call_moonshot_with_history(conversation_history)
            elif self.ai_service == 'deepseek':
                return self._call_deepseek_with_history(conversation_history)
            else:
                return self._call_demo_ai_with_history(keyword, news_content)

        except Exception as e:
            print(f"❌ AI服务调用失败: {str(e)}")
            return self._call_demo_ai_with_history(keyword, news_content)

    def _call_openai_with_history(self, conversation_history: List[Dict]) -> str:
        """
        使用对话历史调用OpenAI API
        这是优化后的方法，支持完整的对话历史
        """
        import time

        try:
            from openai import OpenAI

            # 获取配置参数
            api_key = os.getenv("OPENAI_API_KEY")
            if not api_key:
                raise ValueError("未设置OPENAI_API_KEY环境变量")

            model = os.getenv("OPENAI_MODEL", "gpt-3.5-turbo")
            max_tokens = int(os.getenv("OPENAI_MAX_TOKENS", "4000"))  # 增加token数量以支持对话历史
            temperature = float(os.getenv("OPENAI_TEMPERATURE", "0.7"))

            print(f"🤖 正在调用OpenAI API (对话历史模式)...")
            print(f"   模型: {model}")
            print(f"   最大tokens: {max_tokens}")
            print(f"   温度参数: {temperature}")
            print(f"   对话消息数: {len(conversation_history)}")

            client = OpenAI(api_key=api_key)

            # 重试机制
            max_retries = 3
            for attempt in range(max_retries):
                try:
                    response = client.chat.completions.create(
                        model=model,
                        messages=conversation_history,  # 使用完整的对话历史
                        max_tokens=max_tokens,
                        temperature=temperature,
                        top_p=0.9,  # 提高生成质量
                        frequency_penalty=0.1,  # 减少重复内容
                        presence_penalty=0.1,   # 鼓励多样性
                    )

                    article = response.choices[0].message.content

                    # 检查生成内容质量
                    if not article or len(article.strip()) < 200:
                        raise ValueError("生成的文章内容过短，可能存在问题")

                    print(f"✅ OpenAI生成文章完成")
                    print(f"   文章长度: {len(article)} 字符")
                    print(f"   使用tokens: {response.usage.total_tokens if response.usage else '未知'}")
                    print(f"   重试次数: {attempt + 1}/{max_retries}")

                    return article

                except Exception as retry_error:
                    print(f"⚠️ OpenAI调用第{attempt + 1}次尝试失败: {str(retry_error)}")
                    if attempt < max_retries - 1:
                        wait_time = (attempt + 1) * 2  # 递增等待时间
                        print(f"   等待{wait_time}秒后重试...")
                        time.sleep(wait_time)
                    else:
                        raise retry_error

        except ImportError:
            error_msg = "未安装openai库，请运行: pip install openai"
            print(f"❌ {error_msg}")
            raise ImportError(error_msg)
        except Exception as e:
            error_msg = f"OpenAI调用失败: {str(e)}"
            print(f"❌ {error_msg}")
            raise e

    def _call_claude_with_history(self, conversation_history: List[Dict]) -> str:
        """使用对话历史调用Claude API"""
        try:
            import anthropic

            client = anthropic.Anthropic(api_key=os.getenv("CLAUDE_API_KEY"))

            # Claude需要分离系统消息和用户消息
            system_message = ""
            messages = []

            for msg in conversation_history:
                if msg["role"] == "system":
                    system_message = msg["content"]
                else:
                    messages.append(msg)

            response = client.messages.create(
                model=os.getenv("CLAUDE_MODEL", "claude-3-sonnet-20240229"),
                max_tokens=int(os.getenv("CLAUDE_MAX_TOKENS", "3000")),
                temperature=float(os.getenv("CLAUDE_TEMPERATURE", "0.7")),
                system=system_message,
                messages=messages
            )

            article = response.content[0].text
            print(f"✅ Claude生成文章完成，长度: {len(article)} 字符")
            return article

        except Exception as e:
            print(f"❌ Claude调用失败: {str(e)}")
            raise e

    def _call_groq_with_history(self, conversation_history: List[Dict]) -> str:
        """使用对话历史调用Groq API"""
        try:
            from groq import Groq

            client = Groq(api_key=os.getenv("GROQ_API_KEY"))

            response = client.chat.completions.create(
                model=os.getenv("GROQ_MODEL", "llama3-8b-8192"),
                messages=conversation_history,
                max_tokens=int(os.getenv("GROQ_MAX_TOKENS", "3000")),
                temperature=float(os.getenv("GROQ_TEMPERATURE", "0.7")),
            )

            article = response.choices[0].message.content
            print(f"✅ Groq生成文章完成，长度: {len(article)} 字符")
            return article

        except Exception as e:
            print(f"❌ Groq调用失败: {str(e)}")
            raise e

    def _call_moonshot_with_history(self, conversation_history: List[Dict]) -> str:
        """使用对话历史调用Moonshot API"""
        try:
            from openai import OpenAI

            client = OpenAI(
                api_key=os.getenv("MOONSHOT_API_KEY"),
                base_url="https://api.moonshot.cn/v1"
            )

            response = client.chat.completions.create(
                model=os.getenv("MOONSHOT_MODEL", "moonshot-v1-8k"),
                messages=conversation_history,
                max_tokens=int(os.getenv("MOONSHOT_MAX_TOKENS", "3000")),
                temperature=float(os.getenv("MOONSHOT_TEMPERATURE", "0.7")),
            )

            article = response.choices[0].message.content
            print(f"✅ Moonshot生成文章完成，长度: {len(article)} 字符")
            return article

        except Exception as e:
            print(f"❌ Moonshot调用失败: {str(e)}")
            raise e

    def _call_deepseek_with_history(self, conversation_history: List[Dict]) -> str:
        """使用对话历史调用DeepSeek API"""
        try:
            from openai import OpenAI

            client = OpenAI(
                api_key=os.getenv("DEEPSEEK_API_KEY"),
                base_url="https://api.deepseek.com"
            )

            response = client.chat.completions.create(
                model=os.getenv("DEEPSEEK_MODEL", "deepseek-chat"),
                messages=conversation_history,
                max_tokens=int(os.getenv("DEEPSEEK_MAX_TOKENS", "3000")),
                temperature=float(os.getenv("DEEPSEEK_TEMPERATURE", "0.7")),
            )

            article = response.choices[0].message.content
            print(f"✅ DeepSeek生成文章完成，长度: {len(article)} 字符")
            return article

        except Exception as e:
            print(f"❌ DeepSeek调用失败: {str(e)}")
            raise e

    def _call_demo_ai_with_history(self, keyword: str, news_content: str) -> str:
        """
        演示模式AI（模拟生成文章）
        支持对话历史模式
        """
        print("🎭 使用演示模式生成文章 (对话历史模式)...")
        print(f"   关键词: {keyword}")
        print(f"   新闻内容长度: {len(news_content)} 字符")

        # 模拟AI生成的文章（根据关键词和新闻内容定制）
        demo_article = f"""# 重磅！{keyword}领域迎来新突破，这些变化值得关注

## 🔥 最新动态抢先看

近期，{keyword}领域再次成为全球关注的焦点。从技术突破到应用落地，相关发展正在以前所未有的速度改变着我们的世界。

根据最新的行业报告和新闻资讯，{keyword}在多个维度都展现出了强劲的发展势头。

## 📊 核心发展趋势

当前{keyword}的发展呈现出几个显著特点：

### 1. 技术能力持续提升
最新的技术突破表明，{keyword}的核心能力正在快速提升。无论是在性能指标还是应用效果上，都达到了新的高度。

### 2. 应用场景日益丰富
从传统领域到新兴行业，{keyword}的应用边界不断扩展。越来越多的实际场景开始受益于这项技术。

### 3. 产业化进程加速
市场数据显示，{keyword}相关的产业化进程正在加速。更多企业开始将相关技术集成到实际业务中。

## 🎯 深度分析：机遇与挑战并存

{keyword}的快速发展带来了前所未有的机遇，同时也伴随着新的挑战：

**机遇方面：**
- 💡 创新效率大幅提升
- 🚀 新商业模式不断涌现
- 🌟 用户体验显著改善

**挑战方面：**
- ⚠️ 技术标准有待完善
- 🔒 安全隐私需要保障
- 📚 人才培养亟需跟上

## 🔮 未来展望

展望未来，{keyword}将继续深度融入我们的日常生活和工作中。专家预测，在接下来的几年里，我们将看到更多突破性的进展。

对于普通用户而言，这意味着：
- 更智能的产品和服务
- 更便捷的使用体验
- 更多的创新可能性

对于行业从业者来说，则需要：
- 持续关注技术发展动态
- 积极拥抱变化和创新
- 加强相关技能的学习

## 💭 结语

{keyword}的发展正处在一个关键的转折点。我们需要在拥抱技术进步的同时，也要理性思考如何更好地应对相关挑战。

只有通过持续的创新、合理的规划和广泛的合作，我们才能确保{keyword}技术真正造福社会，为人类创造更美好的未来。

你对{keyword}的发展有什么看法？欢迎在评论区分享你的观点！

---

*本文基于最新新闻资讯整理，内容仅供参考。*

📅 生成时间：{datetime.now().strftime('%Y年%m月%d日 %H:%M:%S')}
🤖 生成模式：演示模式 (Demo AI with Conversation History)
📰 新闻素材：已整合 {len(news_content)} 字符的新闻内容
"""

        print(f"✅ 演示文章生成完成")
        print(f"   关键词: {keyword}")
        print(f"   文章长度: {len(demo_article)} 字符")
        print(f"   新闻素材长度: {len(news_content)} 字符")

        return demo_article

    def generate_article(self, keyword: str) -> Dict:
        """
        生成新闻文章（主要功能）

        Args:
            keyword: 搜索关键词

        Returns:
            包含文章内容和元数据的字典
        """
        print(f"\n🚀 开始生成新闻文章")
        print(f"关键词: '{keyword}'")
        print("=" * 60)

        start_time = time.time()

        try:
            # 步骤1: 搜索新闻
            news_list = self.search_news(keyword)

            if not news_list:
                return {
                    'success': False,
                    'error': '未找到相关新闻',
                    'keyword': keyword,
                    'timestamp': datetime.now().isoformat()
                }

            # 步骤2: 整合新闻内容
            integrated_content = self.integrate_news_content(news_list)

            # 步骤3: 调用AI生成文章（使用对话历史模式，符合PRD要求）
            article_content = self.call_ai_service(keyword, integrated_content)

            # 计算耗时
            elapsed_time = time.time() - start_time

            # 构建返回结果
            result = {
                'success': True,
                'keyword': keyword,
                'article_content': article_content,
                'news_count': len(news_list),
                'news_list': news_list,
                'integrated_content': integrated_content,
                'ai_service': self.ai_service,
                'elapsed_time': round(elapsed_time, 2),
                'timestamp': datetime.now().isoformat(),
                'article_length': len(article_content),
                'metadata': {
                    'style': self.article_style,
                    'target_length': self.article_length,
                    'max_news_count': self.max_news_count
                }
            }

            print(f"\n🎉 文章生成完成！")
            print(f"   耗时: {elapsed_time:.2f} 秒")
            print(f"   新闻数量: {len(news_list)} 条")
            print(f"   文章长度: {len(article_content)} 字符")
            print(f"   AI服务: {self.ai_service}")

            return result

        except Exception as e:
            error_msg = f"生成文章时出错: {str(e)}"
            print(f"❌ {error_msg}")

            return {
                'success': False,
                'error': error_msg,
                'keyword': keyword,
                'elapsed_time': round(time.time() - start_time, 2),
                'timestamp': datetime.now().isoformat()
            }

    def save_article(self, result: Dict, output_dir: str = "output") -> str:
        """
        保存生成的文章

        Args:
            result: 生成结果
            output_dir: 输出目录

        Returns:
            保存的文件路径
        """
        if not result.get('success'):
            print("❌ 无法保存失败的生成结果")
            return ""

        # 创建输出目录
        os.makedirs(output_dir, exist_ok=True)

        # 生成文件名
        keyword = result['keyword']
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f"article_{keyword}_{timestamp}.md"
        filepath = os.path.join(output_dir, filename)

        # 构建文件内容
        content = f"""# 新闻文章生成结果

## 基本信息
- **关键词**: {result['keyword']}
- **生成时间**: {result['timestamp']}
- **耗时**: {result['elapsed_time']} 秒
- **AI服务**: {result['ai_service']}
- **新闻数量**: {result['news_count']} 条
- **文章长度**: {result['article_length']} 字符

## 生成的文章

{result['article_content']}

## 新闻来源

{result['integrated_content']}

---
*本文由新闻文章生成器自动生成*
"""

        # 保存文件
        try:
            with open(filepath, 'w', encoding='utf-8') as f:
                f.write(content)

            print(f"✅ 文章已保存到: {filepath}")
            return filepath

        except Exception as e:
            print(f"❌ 保存文章失败: {str(e)}")
            return ""


def main():
    """主函数 - 演示功能"""
    print("📰 新闻文章生成器演示")
    print("=" * 60)

    # 初始化生成器
    generator = NewsArticleGenerator()

    # 测试关键词列表
    test_keywords = [
        "人工智能",
        "新能源汽车",
        "区块链技术",
        "元宇宙",
        "量子计算"
    ]

    print(f"\n🎯 测试关键词: {', '.join(test_keywords)}")

    # 让用户选择关键词
    try:
        print(f"\n请选择测试关键词:")
        for i, keyword in enumerate(test_keywords, 1):
            print(f"   {i}. {keyword}")
        print(f"   0. 自定义关键词")

        choice = input("\n请输入选择 (1-5 或 0): ").strip()

        if choice == '0':
            keyword = input("请输入自定义关键词: ").strip()
            if not keyword:
                keyword = "人工智能"  # 默认关键词
        elif choice.isdigit() and 1 <= int(choice) <= len(test_keywords):
            keyword = test_keywords[int(choice) - 1]
        else:
            keyword = test_keywords[0]  # 默认第一个

        print(f"\n🎯 选择的关键词: '{keyword}'")

        # 生成文章
        result = generator.generate_article(keyword)

        # 保存文章
        if result.get('success'):
            filepath = generator.save_article(result)

            # 显示文章预览
            article = result['article_content']
            preview_length = 300

            print(f"\n📄 文章预览 (前{preview_length}字符):")
            print("-" * 50)
            print(article[:preview_length] + ("..." if len(article) > preview_length else ""))
            print("-" * 50)

            if filepath:
                print(f"\n💾 完整文章已保存到: {filepath}")
        else:
            print(f"\n❌ 生成失败: {result.get('error', '未知错误')}")

    except KeyboardInterrupt:
        print(f"\n\n👋 用户中断，程序退出")
    except Exception as e:
        print(f"\n❌ 程序出错: {str(e)}")


if __name__ == "__main__":
    main()
