#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
优化的新闻文章生成器演示脚本
=================================

实现PRD要求的核心功能：
1. 输入关键词，检索新闻
2. 将新闻结果统一放入大模型的历史对话当中
3. 最终生成一篇公众号文章
4. 优先使用OpenAI

优化特性：
- 完善的错误处理和用户友好提示
- 清晰的流程步骤和进度显示
- 详细的中文注释和代码文档
- 模块化的功能设计
- 智能的配置检查和验证
"""

import os
import sys
import time
import traceback
from datetime import datetime
from typing import Optional, Dict, Any
from dotenv import load_dotenv

# 添加当前目录到Python路径，确保模块导入正常
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

try:
    from news_article_generator import NewsArticleGenerator
except ImportError as e:
    print(f"❌ 模块导入失败: {e}")
    print(f"请确保 news_article_generator.py 文件存在于当前目录")
    sys.exit(1)

# 加载环境变量配置
load_dotenv()


def show_welcome() -> None:
    """
    显示程序欢迎信息和功能特性

    展示程序的主要功能和优化特性，为用户提供清晰的程序概览
    """
    print("🚀 优化的新闻文章生成器")
    print("=" * 60)
    print("📋 核心功能特性:")
    print("   ✅ 智能新闻搜索和筛选 (支持多种搜索引擎)")
    print("   ✅ 对话历史管理 (PRD核心要求)")
    print("   ✅ 优先使用OpenAI (PRD要求)")
    print("   ✅ 高质量公众号文章生成")
    print("   ✅ 完整的中文注释和错误处理")
    print("   ✅ 模块化设计和流程优化")
    print()
    print("🎯 工作流程:")
    print("   1️⃣ 输入关键词 → 2️⃣ 检索新闻 → 3️⃣ 构建对话历史 → 4️⃣ 生成文章")
    print()


def check_configuration() -> Dict[str, Any]:
    """
    检查系统配置状态

    Returns:
        Dict[str, Any]: 配置检查结果，包含各项配置的状态信息
    """
    print("🔧 系统配置检查:")
    print("-" * 40)

    config_status = {
        'search_service': False,
        'openai_available': False,
        'backup_ai_services': [],
        'total_ai_services': 0,
        'warnings': []
    }

    # 检查搜索服务配置
    search_service = os.getenv('SEARCH_SERVICE', '').strip()
    if search_service:
        print(f"✅ 搜索服务: {search_service}")
        config_status['search_service'] = True
        config_status['search_service_name'] = search_service
    else:
        print(f"⚠️ 搜索服务: 未配置")
        print(f"   💡 建议在.env文件中设置 SEARCH_SERVICE=brave")
        config_status['warnings'].append("搜索服务未配置")

    # 检查OpenAI配置（优先级最高）
    openai_key = os.getenv('OPENAI_API_KEY', '').strip()
    if openai_key and openai_key != 'your_openai_api_key_here':
        print(f"✅ OpenAI API: 已配置 (优先使用)")
        config_status['openai_available'] = True
        config_status['total_ai_services'] += 1
    else:
        print(f"⚠️ OpenAI API: 未配置")
        print(f"   💡 建议在.env文件中设置 OPENAI_API_KEY")
        config_status['warnings'].append("OpenAI API未配置")

    # 检查备用AI服务
    backup_ai_services = {
        'Claude': os.getenv('CLAUDE_API_KEY', '').strip(),
        'Groq': os.getenv('GROQ_API_KEY', '').strip(),
        'Moonshot': os.getenv('MOONSHOT_API_KEY', '').strip(),
        'DeepSeek': os.getenv('DEEPSEEK_API_KEY', '').strip()
    }

    configured_backup_services = []
    for service_name, api_key in backup_ai_services.items():
        if api_key and not api_key.endswith('_here'):
            configured_backup_services.append(service_name)
            config_status['total_ai_services'] += 1

    config_status['backup_ai_services'] = configured_backup_services

    if configured_backup_services:
        print(f"✅ 备用AI服务: {', '.join(configured_backup_services)}")
    else:
        print(f"ℹ️ 备用AI服务: 未配置 (将使用演示模式)")
        if not config_status['openai_available']:
            config_status['warnings'].append("无可用AI服务，将使用演示模式")

    # 显示配置总结
    print(f"\n📊 配置总结:")
    print(f"   🔍 搜索服务: {'已配置' if config_status['search_service'] else '未配置'}")
    print(f"   🤖 AI服务总数: {config_status['total_ai_services']}")
    print(f"   ⚠️ 警告数量: {len(config_status['warnings'])}")

    print()
    return config_status


def get_user_keyword() -> Optional[str]:
    """
    获取用户输入的搜索关键词

    提供推荐关键词列表和自定义输入选项，确保用户输入的有效性

    Returns:
        Optional[str]: 用户选择的关键词，如果用户取消则返回None
    """
    print("🎯 搜索关键词选择:")
    print("-" * 40)

    # 分类推荐关键词列表，提供更丰富的选择
    recommended_keywords = {
        "科技类": ["人工智能", "ChatGPT", "量子计算", "5G技术"],
        "产业类": ["新能源汽车", "区块链技术", "元宇宙", "自动驾驶"],
        "热点类": ["数字经济", "碳中和", "芯片技术", "生物医药"]
    }

    # 展平关键词列表用于选择
    all_keywords = []
    print("📝 推荐关键词 (按类别分组):")

    keyword_index = 1
    for category, keywords in recommended_keywords.items():
        print(f"\n   📂 {category}:")
        for keyword in keywords:
            print(f"      {keyword_index}. {keyword}")
            all_keywords.append(keyword)
            keyword_index += 1

    print(f"\n   0. 💭 自定义关键词")

    try:
        choice = input(f"\n请选择关键词 (1-{len(all_keywords)} 或 0): ").strip()

        if choice == '0':
            # 自定义关键词输入
            print("\n💭 自定义关键词输入:")
            keyword = input("请输入您的搜索关键词: ").strip()

            # 验证关键词有效性
            if not keyword:
                print("⚠️ 关键词不能为空，使用默认关键词")
                keyword = all_keywords[0]
            elif len(keyword) > 50:
                print("⚠️ 关键词过长，已截取前50个字符")
                keyword = keyword[:50]
            elif len(keyword) < 2:
                print("⚠️ 关键词过短，使用默认关键词")
                keyword = all_keywords[0]

            print(f"✅ 已选择自定义关键词: '{keyword}'")

        elif choice.isdigit() and 1 <= int(choice) <= len(all_keywords):
            # 选择推荐关键词
            keyword = all_keywords[int(choice) - 1]
            print(f"✅ 已选择推荐关键词: '{keyword}'")

        else:
            # 无效选择，使用默认关键词
            print("⚠️ 无效选择，使用默认关键词")
            keyword = all_keywords[0]
            print(f"✅ 使用默认关键词: '{keyword}'")

        return keyword

    except (KeyboardInterrupt, EOFError):
        print("\n👋 用户取消操作")
        return None
    except Exception as e:
        print(f"\n❌ 输入处理出错: {e}")
        print(f"✅ 使用默认关键词: '{all_keywords[0]}'")
        return all_keywords[0]


def demonstrate_core_workflow(generator: NewsArticleGenerator, keyword: str) -> bool:
    """
    演示核心工作流程，实现PRD要求的业务逻辑

    按照PRD要求的步骤执行完整的新闻文章生成流程：
    1. 输入关键词，检索新闻
    2. 将新闻结果统一放入大模型的历史对话当中
    3. 最终生成一篇公众号文章

    Args:
        generator: 新闻文章生成器实例
        keyword: 搜索关键词

    Returns:
        bool: 执行是否成功
    """
    print(f"\n🔄 开始执行核心业务流程")
    print("=" * 60)
    print(f"🎯 目标关键词: '{keyword}'")
    print(f"⏰ 开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()

    # 记录开始时间用于计算总耗时
    start_time = time.time()
    workflow_steps = []  # 记录每个步骤的执行情况

    try:
        # 步骤1: 输入关键词，检索新闻 (PRD要求)
        print("📝 步骤1: 输入关键词，检索新闻")
        print("-" * 50)
        step_start = time.time()

        news_list = generator.search_news(keyword)
        step_time = time.time() - step_start

        if not news_list:
            print("❌ 未找到相关新闻，无法继续执行后续步骤")
            workflow_steps.append({"step": 1, "status": "failed", "time": step_time})
            return False

        print(f"✅ 成功检索到 {len(news_list)} 条新闻")
        workflow_steps.append({"step": 1, "status": "success", "time": step_time, "count": len(news_list)})

        # 显示新闻质量分析
        print(f"\n📊 新闻质量分析:")
        high_quality = len([n for n in news_list if n.get('relevance_score', 0) >= 70])
        medium_quality = len([n for n in news_list if 40 <= n.get('relevance_score', 0) < 70])
        low_quality = len(news_list) - high_quality - medium_quality

        print(f"   🔥 高质量新闻: {high_quality} 条 (相关性≥70分)")
        print(f"   📢 中等质量新闻: {medium_quality} 条 (相关性40-69分)")
        print(f"   📋 低质量新闻: {low_quality} 条 (相关性<40分)")
        print(f"   ⏱️ 检索耗时: {step_time:.2f} 秒")

        # 步骤2: 整合新闻内容
        print(f"\n📝 步骤2: 整合新闻内容")
        print("-" * 50)
        step_start = time.time()

        integrated_content = generator.integrate_news_content(news_list)
        step_time = time.time() - step_start

        if not integrated_content:
            print("❌ 新闻内容整合失败")
            workflow_steps.append({"step": 2, "status": "failed", "time": step_time})
            return False

        print(f"✅ 新闻内容整合完成")
        print(f"   📄 总长度: {len(integrated_content)} 字符")
        print(f"   ⏱️ 整合耗时: {step_time:.2f} 秒")
        workflow_steps.append({"step": 2, "status": "success", "time": step_time, "length": len(integrated_content)})

        # 步骤3: 将新闻结果统一放入大模型的历史对话当中（PRD核心要求）
        print(f"\n📝 步骤3: 构建对话历史（PRD核心要求）")
        print("-" * 50)
        step_start = time.time()

        conversation_history = generator.build_conversation_history(keyword, integrated_content)
        step_time = time.time() - step_start

        if not conversation_history:
            print("❌ 对话历史构建失败")
            workflow_steps.append({"step": 3, "status": "failed", "time": step_time})
            return False

        print(f"✅ 对话历史构建完成")
        print(f"   💬 消息数量: {len(conversation_history)} 条")
        print(f"   📋 包含系统消息、用户请求、助手确认和新闻素材")
        print(f"   ⏱️ 构建耗时: {step_time:.2f} 秒")
        workflow_steps.append({"step": 3, "status": "success", "time": step_time, "messages": len(conversation_history)})

        # 步骤4: 最终生成一篇公众号文章 (PRD要求)
        print(f"\n📝 步骤4: 生成公众号文章")
        print("-" * 50)
        step_start = time.time()

        print(f"🤖 使用AI服务: {generator.ai_service} (OpenAI优先)")
        print(f"📝 正在生成文章，请稍候...")

        article_content = generator.call_ai_service(keyword, integrated_content)
        step_time = time.time() - step_start

        if not article_content:
            print("❌ 文章生成失败")
            workflow_steps.append({"step": 4, "status": "failed", "time": step_time})
            return False

        print(f"✅ 公众号文章生成完成")
        print(f"   📄 文章长度: {len(article_content)} 字符")
        print(f"   🤖 AI服务: {generator.ai_service}")
        print(f"   ⏱️ 生成耗时: {step_time:.2f} 秒")
        workflow_steps.append({"step": 4, "status": "success", "time": step_time, "length": len(article_content)})

        # 计算总耗时和显示执行总结
        total_elapsed_time = time.time() - start_time
        print(f"\n📊 执行总结:")
        print(f"   ⏰ 总耗时: {total_elapsed_time:.2f} 秒")
        print(f"   ✅ 成功步骤: {len([s for s in workflow_steps if s['status'] == 'success'])}/4")

        # 显示文章预览
        preview_length = 500
        print(f"\n📄 文章预览 (前{preview_length}字符):")
        print("=" * 60)
        print(article_content[:preview_length] + ("..." if len(article_content) > preview_length else ""))
        print("=" * 60)

        # 询问是否保存文章
        save_choice = input(f"\n💾 是否保存完整文章到文件？(y/n): ").strip().lower()

        if save_choice in ['y', 'yes', '是', '']:
            # 构建完整的生成结果
            result = {
                'success': True,
                'keyword': keyword,
                'article_content': article_content,
                'news_count': len(news_list),
                'news_list': news_list,
                'integrated_content': integrated_content,
                'ai_service': generator.ai_service,
                'elapsed_time': total_elapsed_time,  # 使用正确的变量名
                'timestamp': datetime.now().isoformat(),
                'article_length': len(article_content),
                'optimization_mode': True,
                'workflow_steps': workflow_steps  # 添加工作流程步骤记录
            }

            # 保存文章到文件
            filepath = generator.save_article(result, "optimized_output")

            if filepath:
                print(f"✅ 文章已成功保存到: {filepath}")
                print(f"📁 文件包含完整的文章内容和新闻来源信息")
            else:
                print(f"❌ 文章保存失败，请检查文件权限")
        else:
            print(f"📝 文章未保存，但生成流程已完成")

        return True

    except Exception as e:
        print(f"❌ 执行过程中发生错误: {str(e)}")
        print(f"🔍 错误详情: {traceback.format_exc()}")
        return False


def initialize_generator() -> Optional[NewsArticleGenerator]:
    """
    初始化新闻文章生成器

    Returns:
        Optional[NewsArticleGenerator]: 初始化成功返回生成器实例，失败返回None
    """
    print("🔧 正在初始化新闻文章生成器...")

    try:
        generator = NewsArticleGenerator()

        # 验证生成器初始化状态
        if not hasattr(generator, 'search_service') or not hasattr(generator, 'ai_service'):
            print("❌ 生成器初始化不完整，缺少必要组件")
            return None

        print(f"✅ 生成器初始化完成")

        # 显示详细的初始化信息
        search_service_name = getattr(generator.search_service, 'search_service', 'unknown')
        print(f"   🔍 搜索服务: {search_service_name}")
        print(f"   🤖 AI服务: {generator.ai_service}")
        print(f"   📰 最大新闻数: {getattr(generator, 'max_news_count', 'unknown')}")
        print(f"   📝 文章风格: {getattr(generator, 'article_style', 'unknown')}")
        print()

        return generator

    except Exception as e:
        print(f"❌ 生成器初始化失败: {str(e)}")
        print(f"🔍 错误详情: {traceback.format_exc()}")
        return None


def main() -> None:
    """
    主函数 - 程序入口点

    执行完整的新闻文章生成流程，包括：
    1. 显示欢迎信息
    2. 检查系统配置
    3. 初始化生成器
    4. 获取用户输入的关键词
    5. 执行核心工作流程
    6. 显示执行结果
    """
    program_start_time = time.time()

    try:
        # 步骤1: 显示欢迎信息
        show_welcome()

        # 步骤2: 检查系统配置
        config_status = check_configuration()

        # 如果有严重配置问题，询问是否继续
        if config_status['warnings']:
            print(f"⚠️ 发现 {len(config_status['warnings'])} 个配置警告:")
            for warning in config_status['warnings']:
                print(f"   • {warning}")

            continue_choice = input(f"\n是否继续执行？(y/n): ").strip().lower()
            if continue_choice not in ['y', 'yes', '是', '']:
                print("👋 程序已退出")
                return
            print()

        # 步骤3: 初始化生成器
        generator = initialize_generator()
        if not generator:
            print("❌ 无法继续执行，生成器初始化失败")
            return

        # 步骤4: 获取用户输入的关键词
        keyword = get_user_keyword()
        if not keyword:
            print("👋 用户取消操作，程序退出")
            return

        # 步骤5: 执行核心工作流程
        success = demonstrate_core_workflow(generator, keyword)

        # 步骤6: 显示最终执行结果
        program_total_time = time.time() - program_start_time

        if success:
            print(f"\n🎉 优化的新闻文章生成流程执行成功！")
            print("=" * 60)
            print(f"✅ 已完整实现PRD要求的所有核心功能:")
            print(f"   1. ✅ 输入关键词，检索新闻")
            print(f"   2. ✅ 将新闻结果统一放入大模型的历史对话当中")
            print(f"   3. ✅ 最终生成一篇公众号文章")
            print(f"   4. ✅ 优先使用OpenAI")
            print()
            print(f"📊 程序执行统计:")
            print(f"   ⏰ 程序总耗时: {program_total_time:.2f} 秒")
            print(f"   🎯 目标关键词: '{keyword}'")
            print(f"   🤖 使用AI服务: {generator.ai_service}")
            print(f"   🔍 使用搜索服务: {getattr(generator.search_service, 'search_service', 'unknown')}")
        else:
            print(f"\n❌ 执行失败")
            print("=" * 60)
            print(f"请检查以下可能的问题:")
            print(f"   • 网络连接是否正常")
            print(f"   • API密钥是否正确配置")
            print(f"   • 搜索服务是否可用")
            print(f"   • AI服务是否可用")
            print()
            print(f"💡 建议:")
            print(f"   • 检查.env文件中的配置")
            print(f"   • 查看上方的详细错误信息")
            print(f"   • 尝试使用不同的搜索关键词")

    except KeyboardInterrupt:
        print(f"\n\n👋 用户中断程序")
        print(f"程序已安全退出")

    except Exception as e:
        print(f"\n❌ 程序发生未预期的异常: {str(e)}")
        print(f"🔍 错误详情: {traceback.format_exc()}")
        print(f"\n💡 如果问题持续存在，请检查:")
        print(f"   • Python环境和依赖包是否正确安装")
        print(f"   • 配置文件是否正确设置")
        print(f"   • 相关模块文件是否存在")


# ==================== 辅助函数 ====================

def print_separator(title: str = "", length: int = 60) -> None:
    """
    打印分隔线，用于美化输出格式

    Args:
        title: 分隔线中间的标题
        length: 分隔线长度
    """
    if title:
        title_with_spaces = f" {title} "
        padding = (length - len(title_with_spaces)) // 2
        separator = "=" * padding + title_with_spaces + "=" * padding
        if len(separator) < length:
            separator += "="
    else:
        separator = "=" * length

    print(separator)


def validate_environment() -> bool:
    """
    验证运行环境是否满足要求

    Returns:
        bool: 环境验证是否通过
    """
    print("🔍 验证运行环境...")

    # 检查Python版本
    python_version = sys.version_info
    if python_version.major < 3 or (python_version.major == 3 and python_version.minor < 7):
        print(f"❌ Python版本过低: {python_version.major}.{python_version.minor}")
        print(f"   要求Python 3.7或更高版本")
        return False

    print(f"✅ Python版本: {python_version.major}.{python_version.minor}.{python_version.micro}")

    # 检查必要的模块
    required_modules = ['requests', 'dotenv', 'json', 'datetime']
    missing_modules = []

    for module in required_modules:
        try:
            __import__(module)
        except ImportError:
            missing_modules.append(module)

    if missing_modules:
        print(f"❌ 缺少必要模块: {', '.join(missing_modules)}")
        print(f"   请使用 pip install 安装缺少的模块")
        return False

    print(f"✅ 所有必要模块已安装")
    return True


def show_help_info() -> None:
    """显示帮助信息"""
    print_separator("帮助信息")
    print("📚 使用说明:")
    print("   1. 确保已正确配置.env文件")
    print("   2. 设置搜索服务 (推荐使用brave)")
    print("   3. 配置AI服务API密钥 (优先OpenAI)")
    print("   4. 运行程序并按提示操作")
    print()
    print("🔧 配置文件示例 (.env):")
    print("   SEARCH_SERVICE=brave")
    print("   BRAVE_API_TOKEN=your_brave_token")
    print("   OPENAI_API_KEY=your_openai_key")
    print()
    print("💡 常见问题:")
    print("   • 如果搜索失败，检查搜索服务配置")
    print("   • 如果AI生成失败，检查API密钥")
    print("   • 如果程序崩溃，查看错误详情")
    print_separator()


if __name__ == "__main__":
    # 验证运行环境
    if not validate_environment():
        print("\n❌ 环境验证失败，程序无法运行")
        show_help_info()
        sys.exit(1)

    # 运行主程序
    main()
