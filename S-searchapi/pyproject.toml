[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "search-enhanced-llm"
version = "1.0.0"
description = "搜索增强大语言模型服务 - 新闻文章生成器"
readme = "README.md"
license = {text = "MIT"}
authors = [
    {name = "Search Enhanced LLM Team", email = "<EMAIL>"}
]
keywords = ["search", "llm", "news", "article", "generator", "ai"]
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: MIT License",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.8",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Topic :: Software Development :: Libraries :: Python Modules",
    "Topic :: Internet :: WWW/HTTP :: Indexing/Search",
    "Topic :: Scientific/Engineering :: Artificial Intelligence",
]
requires-python = ">=3.8"

# 核心依赖
dependencies = [
    "requests>=2.25.1",
    "python-dotenv>=0.19.0",
    "flask>=2.0.0",
    "openai>=1.0.0",  # 优先使用OpenAI
]

# 可选依赖组
[project.optional-dependencies]
# 搜索引擎支持
search = [
    "duckduckgo-search>=3.8.0",
    "beautifulsoup4>=4.9.3",
    "lxml>=4.6.3",
]

# AI服务支持
ai = [
    "anthropic>=0.3.0",  # Claude API
    "groq>=0.4.0",       # Groq API
]

# 开发依赖
dev = [
    "pytest>=7.0.0",
    "pytest-cov>=4.0.0",
    "black>=22.0.0",
    "flake8>=5.0.0",
    "mypy>=1.0.0",
]

# 完整安装（包含所有可选依赖）
full = [
    "search-enhanced-llm[search,ai,dev]",
]

[project.urls]
Homepage = "https://github.com/example/search-enhanced-llm"
Documentation = "https://github.com/example/search-enhanced-llm/blob/main/README.md"
Repository = "https://github.com/example/search-enhanced-llm.git"
Issues = "https://github.com/example/search-enhanced-llm/issues"

[project.scripts]
# 命令行工具
news-generator = "news_article_generator:main"
search-api = "news_article_api:main"

[tool.setuptools.packages.find]
where = ["."]
include = ["*.py"]

[tool.black]
line-length = 88
target-version = ['py38']
include = '\.pyi?$'
extend-exclude = '''
/(
  # 排除的目录
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | _build
  | buck-out
  | build
  | dist
  | __pycache__
)/
'''

[tool.flake8]
max-line-length = 88
extend-ignore = ["E203", "W503"]
exclude = [
    ".git",
    "__pycache__",
    "build",
    "dist",
    ".eggs",
    "*.egg-info",
    ".venv",
    ".env"
]

[tool.mypy]
python_version = "3.8"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = ["test_*.py", "*_test.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
addopts = [
    "--cov=.",
    "--cov-report=html",
    "--cov-report=term-missing",
    "--verbose"
]

# 环境变量配置示例
[tool.env]
# 搜索服务配置
SEARCH_SERVICE = "brave"  # 推荐使用brave搜索
MAX_RESULTS = "10"

# AI服务配置 - 优先使用OpenAI
OPENAI_API_KEY = "your_openai_api_key_here"
OPENAI_MODEL = "gpt-3.5-turbo"
OPENAI_MAX_TOKENS = "3000"
OPENAI_TEMPERATURE = "0.7"

# 新闻文章生成器配置
NEWS_MAX_COUNT = "10"
ARTICLE_STYLE = "公众号"
ARTICLE_LENGTH = "800-1200字"

# API服务配置
API_HOST = "0.0.0.0"
API_PORT = "5000"
API_DEBUG = "false"
MAX_BATCH_SIZE = "10"
