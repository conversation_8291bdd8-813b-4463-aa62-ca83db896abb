#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Google API配置助手
交互式配置Google自定义搜索API
"""

import os
import re


def print_header():
    """打印标题"""
    print("🔧 Google API配置助手")
    print("=" * 50)
    print("本工具将帮助您配置Google自定义搜索API")
    print()


def print_step(step_num, title):
    """打印步骤标题"""
    print(f"\n📋 步骤 {step_num}: {title}")
    print("-" * 30)


def validate_api_key(api_key):
    """验证API密钥格式"""
    if not api_key:
        return False, "API密钥不能为空"
    
    if api_key == "your_google_api_key_here":
        return False, "请输入真实的API密钥，不要使用默认值"
    
    if not api_key.startswith("AIza"):
        return False, "Google API密钥通常以'AIza'开头，请检查是否正确"
    
    if len(api_key) < 30:
        return False, "API密钥长度似乎太短，请检查是否完整"
    
    return True, "API密钥格式正确"


def validate_search_engine_id(cx):
    """验证搜索引擎ID格式"""
    if not cx:
        return False, "搜索引擎ID不能为空"
    
    if cx == "your_custom_search_engine_id_here":
        return False, "请输入真实的搜索引擎ID，不要使用默认值"
    
    if ":" not in cx:
        return False, "搜索引擎ID通常包含':'字符，请检查是否正确"
    
    return True, "搜索引擎ID格式正确"


def get_user_input(prompt, validator=None):
    """获取用户输入并验证"""
    while True:
        value = input(prompt).strip()
        
        if validator:
            is_valid, message = validator(value)
            if is_valid:
                print(f"✅ {message}")
                return value
            else:
                print(f"❌ {message}")
                print("请重新输入...")
                continue
        
        return value


def show_instructions():
    """显示获取API密钥的说明"""
    print_step(1, "获取Google API密钥和搜索引擎ID")
    
    print("请按照以下步骤获取所需的配置信息：")
    print()
    
    print("🔑 获取API密钥：")
    print("1. 访问 https://console.cloud.google.com/")
    print("2. 创建项目或选择现有项目")
    print("3. 启用 'Custom Search API'")
    print("4. 创建API密钥")
    print("5. 复制API密钥")
    print()
    
    print("🔍 获取搜索引擎ID：")
    print("1. 访问 https://cse.google.com/")
    print("2. 创建自定义搜索引擎")
    print("3. 在'要搜索的网站'中输入: *")
    print("4. 创建后获取搜索引擎ID")
    print()
    
    print("📖 详细步骤请参考: Google_API_配置指南.md")
    print()


def collect_configuration():
    """收集配置信息"""
    print_step(2, "输入配置信息")
    
    # 获取API密钥
    print("请输入您的Google API密钥:")
    print("(格式类似: AIzaSyABC123DEF456GHI789JKL012MNO345PQR)")
    api_key = get_user_input("Google API密钥: ", validate_api_key)
    
    print()
    
    # 获取搜索引擎ID
    print("请输入您的自定义搜索引擎ID:")
    print("(格式类似: abc123def456:ghi789jkl012)")
    cx = get_user_input("搜索引擎ID: ", validate_search_engine_id)
    
    print()
    
    # 获取其他配置
    print("请输入最大搜索结果数 (默认: 10):")
    max_results = get_user_input("最大结果数 [10]: ")
    if not max_results:
        max_results = "10"
    
    return {
        'api_key': api_key,
        'cx': cx,
        'max_results': max_results
    }


def create_env_file(config):
    """创建.env文件"""
    print_step(3, "创建配置文件")
    
    env_content = f"""# Google自定义搜索API配置
# 由配置助手自动生成

# 搜索服务设置
SEARCH_SERVICE=google
MAX_RESULTS={config['max_results']}
CRAWL_RESULTS=0

# Google API配置
GOOGLE_KEY={config['api_key']}
GOOGLE_CX={config['cx']}

# 备用配置 - 如果需要切换到其他搜索服务，可以取消注释
# SEARCH_SERVICE=duckduckgo
"""
    
    # 检查是否已存在.env文件
    if os.path.exists('.env'):
        print("⚠️  发现现有的.env文件")
        choice = input("是否覆盖现有配置？(y/N): ").strip().lower()
        if choice not in ['y', 'yes']:
            print("❌ 配置已取消")
            return False
        
        # 备份现有文件
        os.rename('.env', '.env.backup')
        print("✅ 已备份现有配置为 .env.backup")
    
    # 写入新配置
    try:
        with open('.env', 'w', encoding='utf-8') as f:
            f.write(env_content)
        
        print("✅ 配置文件已创建: .env")
        return True
        
    except Exception as e:
        print(f"❌ 创建配置文件失败: {str(e)}")
        return False


def test_configuration():
    """测试配置"""
    print_step(4, "测试配置")
    
    print("正在测试Google API配置...")
    
    try:
        # 运行测试脚本
        import subprocess
        result = subprocess.run(['python3', 'google_api_test.py'], 
                              capture_output=True, text=True, timeout=30)
        
        if result.returncode == 0:
            print("✅ 配置测试通过！")
            return True
        else:
            print("❌ 配置测试失败")
            print("错误信息:")
            print(result.stdout)
            print(result.stderr)
            return False
            
    except subprocess.TimeoutExpired:
        print("⚠️  测试超时，请手动运行: python3 google_api_test.py")
        return False
        
    except Exception as e:
        print(f"⚠️  无法自动测试: {str(e)}")
        print("请手动运行: python3 google_api_test.py")
        return False


def show_next_steps():
    """显示后续步骤"""
    print("\n🎉 配置完成！")
    print("=" * 30)
    
    print("后续步骤:")
    print("1. 运行测试: python3 google_api_test.py")
    print("2. 运行演示: python3 demo.py")
    print("3. 查看示例: python3 example_usage.py")
    print()
    
    print("📚 相关文档:")
    print("- README.md - 项目说明")
    print("- Google_API_配置指南.md - 详细配置指南")
    print()
    
    print("💡 提示:")
    print("- 如果遇到问题，请查看配置指南")
    print("- Google API有免费配额限制（每天100次搜索）")
    print("- 可以随时修改.env文件调整配置")


def main():
    """主函数"""
    print_header()
    
    try:
        # 显示说明
        show_instructions()
        
        # 确认用户准备就绪
        ready = input("您是否已经获取了API密钥和搜索引擎ID？(y/N): ").strip().lower()
        if ready not in ['y', 'yes']:
            print("\n请先按照说明获取必要的配置信息，然后重新运行此脚本。")
            print("详细步骤请参考: Google_API_配置指南.md")
            return
        
        # 收集配置
        config = collect_configuration()
        
        # 创建配置文件
        if not create_env_file(config):
            return
        
        # 测试配置
        test_success = test_configuration()
        
        # 显示后续步骤
        show_next_steps()
        
        if test_success:
            print("🎊 恭喜！Google API配置成功！")
        else:
            print("⚠️  配置已完成，但测试未通过，请检查配置是否正确。")
        
    except KeyboardInterrupt:
        print("\n\n❌ 配置已取消")
    except Exception as e:
        print(f"\n❌ 配置过程中出现错误: {str(e)}")


if __name__ == "__main__":
    main()
