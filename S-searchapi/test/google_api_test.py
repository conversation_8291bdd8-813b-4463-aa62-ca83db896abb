#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Google API配置测试工具
用于验证Google自定义搜索API的配置是否正确
"""

import os
import json
import requests
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()


def check_environment_variables():
    """检查环境变量配置"""
    print("🔍 检查环境变量配置...")
    
    required_vars = {
        'SEARCH_SERVICE': os.getenv('SEARCH_SERVICE'),
        'GOOGLE_KEY': os.getenv('GOOGLE_KEY'),
        'GOOGLE_CX': os.getenv('GOOGLE_CX')
    }
    
    issues = []
    
    # 检查搜索服务设置
    if required_vars['SEARCH_SERVICE'] != 'google':
        issues.append(f"❌ SEARCH_SERVICE 应设置为 'google'，当前值: {required_vars['SEARCH_SERVICE']}")
    else:
        print("✅ SEARCH_SERVICE 配置正确")
    
    # 检查Google API密钥
    if not required_vars['GOOGLE_KEY'] or required_vars['GOOGLE_KEY'] == 'your_google_api_key_here':
        issues.append("❌ GOOGLE_KEY 未配置或使用默认值")
    else:
        print("✅ GOOGLE_KEY 已配置")
        # 检查API密钥格式
        if not required_vars['GOOGLE_KEY'].startswith('AIza'):
            issues.append("⚠️  GOOGLE_KEY 格式可能不正确（通常以'AIza'开头）")
    
    # 检查搜索引擎ID
    if not required_vars['GOOGLE_CX'] or required_vars['GOOGLE_CX'] == 'your_custom_search_engine_id_here':
        issues.append("❌ GOOGLE_CX 未配置或使用默认值")
    else:
        print("✅ GOOGLE_CX 已配置")
        # 检查搜索引擎ID格式
        if ':' not in required_vars['GOOGLE_CX']:
            issues.append("⚠️  GOOGLE_CX 格式可能不正确（通常包含':'字符）")
    
    return issues, required_vars


def test_google_api_connection(api_key, cx):
    """测试Google API连接"""
    print("\n🌐 测试Google API连接...")
    
    # 构建测试请求URL
    test_query = "test"
    url = f"https://www.googleapis.com/customsearch/v1"
    
    params = {
        'key': api_key,
        'cx': cx,
        'q': test_query,
        'num': 1  # 只请求1个结果进行测试
    }
    
    try:
        print(f"📡 发送测试请求到: {url}")
        print(f"🔍 测试查询: {test_query}")
        
        response = requests.get(url, params=params, timeout=10)
        
        print(f"📊 响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            
            # 检查响应数据
            if 'items' in data:
                print(f"✅ API连接成功！找到 {len(data['items'])} 个结果")
                
                # 显示第一个结果作为示例
                if data['items']:
                    first_result = data['items'][0]
                    print(f"📄 示例结果:")
                    print(f"   标题: {first_result.get('title', 'N/A')}")
                    print(f"   链接: {first_result.get('link', 'N/A')}")
                    print(f"   摘要: {first_result.get('snippet', 'N/A')[:100]}...")
                
                return True, "API连接成功"
            else:
                print("⚠️  API响应中没有搜索结果")
                return False, "API响应格式异常"
                
        elif response.status_code == 400:
            error_data = response.json()
            error_message = error_data.get('error', {}).get('message', '未知错误')
            print(f"❌ API请求错误: {error_message}")
            return False, f"API请求错误: {error_message}"
            
        elif response.status_code == 403:
            print("❌ API访问被拒绝 - 可能的原因:")
            print("   1. API密钥无效")
            print("   2. Custom Search API未启用")
            print("   3. 超出免费配额")
            print("   4. API密钥有IP或域名限制")
            return False, "API访问被拒绝"
            
        else:
            print(f"❌ 意外的响应状态码: {response.status_code}")
            print(f"响应内容: {response.text[:200]}...")
            return False, f"HTTP错误: {response.status_code}"
            
    except requests.exceptions.Timeout:
        print("❌ 请求超时 - 网络连接可能有问题")
        return False, "请求超时"
        
    except requests.exceptions.ConnectionError:
        print("❌ 连接错误 - 请检查网络连接")
        return False, "连接错误"
        
    except Exception as e:
        print(f"❌ 未知错误: {str(e)}")
        return False, f"未知错误: {str(e)}"


def test_search_functionality():
    """测试完整的搜索功能"""
    print("\n🧪 测试完整搜索功能...")
    
    try:
        from demo import SearchService
        
        # 初始化搜索服务
        search = SearchService()
        
        # 执行测试搜索
        test_query = "Python编程"
        print(f"🔍 执行搜索: {test_query}")
        
        result = search.search(test_query)
        
        # 解析结果
        if isinstance(result, str) and "错误" not in result:
            try:
                data = json.loads(result)
                results = data.get("results", [])
                
                if results:
                    print(f"✅ 搜索功能正常！找到 {len(results)} 个结果")
                    
                    # 显示前2个结果
                    for i, item in enumerate(results[:2], 1):
                        print(f"\n{i}. 标题: {item.get('title', 'N/A')}")
                        print(f"   链接: {item.get('link', 'N/A')}")
                        print(f"   摘要: {item.get('snippet', 'N/A')[:80]}...")
                    
                    return True, "搜索功能正常"
                else:
                    print("⚠️  搜索未返回结果")
                    return False, "搜索无结果"
                    
            except json.JSONDecodeError:
                print("❌ 搜索结果JSON解析失败")
                return False, "JSON解析失败"
        else:
            print(f"❌ 搜索失败: {result}")
            return False, f"搜索失败: {result}"
            
    except ImportError:
        print("❌ 无法导入SearchService - 请检查demo.py文件")
        return False, "导入错误"
        
    except Exception as e:
        print(f"❌ 搜索功能测试失败: {str(e)}")
        return False, f"搜索功能错误: {str(e)}"


def provide_troubleshooting_tips(issues):
    """提供故障排除建议"""
    if not issues:
        return
        
    print("\n🔧 故障排除建议:")
    print("=" * 50)
    
    for issue in issues:
        print(issue)
    
    print("\n📋 解决步骤:")
    print("1. 检查 .env 文件是否存在且格式正确")
    print("2. 确认已按照 'Google_API_配置指南.md' 完成所有配置步骤")
    print("3. 验证Google Cloud Console中的API密钥和权限设置")
    print("4. 确认Custom Search API已启用且有足够配额")
    print("5. 检查网络连接是否正常")
    
    print("\n📚 参考文档:")
    print("- Google_API_配置指南.md")
    print("- https://developers.google.com/custom-search/v1/overview")


def main():
    """主函数"""
    print("🚀 Google API配置测试工具")
    print("=" * 50)
    
    # 第一步：检查环境变量
    issues, env_vars = check_environment_variables()
    
    if issues:
        provide_troubleshooting_tips(issues)
        return
    
    # 第二步：测试API连接
    api_success, api_message = test_google_api_connection(
        env_vars['GOOGLE_KEY'], 
        env_vars['GOOGLE_CX']
    )
    
    if not api_success:
        print(f"\n❌ API连接测试失败: {api_message}")
        provide_troubleshooting_tips([f"API连接问题: {api_message}"])
        return
    
    # 第三步：测试搜索功能
    search_success, search_message = test_search_functionality()
    
    if not search_success:
        print(f"\n❌ 搜索功能测试失败: {search_message}")
        provide_troubleshooting_tips([f"搜索功能问题: {search_message}"])
        return
    
    # 全部测试通过
    print("\n🎉 恭喜！Google API配置完全正确！")
    print("✅ 环境变量配置正确")
    print("✅ API连接正常")
    print("✅ 搜索功能正常")
    print("\n现在您可以正常使用Google搜索API了！")


if __name__ == "__main__":
    main()
