#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Brave新闻搜索测试脚本
专门测试Brave新闻搜索API功能
"""

import os
import json
from dotenv import load_dotenv
from demo import SearchService

# 加载环境变量
load_dotenv()

def test_brave_news_search():
    """测试Brave新闻搜索功能"""
    print("📰 Brave新闻搜索测试")
    print("=" * 60)
    
    # 临时设置为新闻搜索模式
    original_search_type = os.getenv('BRAVE_SEARCH_TYPE')
    os.environ['BRAVE_SEARCH_TYPE'] = 'news'
    
    try:
        # 初始化搜索服务
        search_service = SearchService()
        
        # 检查配置
        print("⚙️ 配置检查:")
        print(f"   搜索服务: {search_service.search_service}")
        print(f"   搜索类型: 新闻搜索")
        print(f"   最大结果数: {search_service.max_results}")
        
        # 检查Brave配置
        brave_token = os.getenv('BRAVE_API_TOKEN')
        brave_search_lang = os.getenv('BRAVE_SEARCH_LANG', 'zh-hans')
        
        print(f"\n🦁 Brave新闻搜索配置:")
        if brave_token and brave_token != 'your_brave_api_token_here':
            print(f"   ✅ API Token: {brave_token[:10]}...{brave_token[-5:]}")
        else:
            print(f"   ❌ API Token: 未配置")
            return
        
        print(f"   搜索语言: {brave_search_lang}")
        print(f"   国家代码: {os.getenv('BRAVE_COUNTRY', 'CN')}")
        print(f"   时间范围: {os.getenv('BRAVE_FRESHNESS', 'all')}")
        
        # 新闻搜索测试用例
        news_queries = [
            ("veo3", "🎬 Veo3 AI视频新闻"),
            ("artificial intelligence", "🤖 人工智能新闻"),
            ("technology trends 2025", "📈 2025科技趋势新闻"),
            ("privacy protection", "🛡️ 隐私保护新闻"),
            ("Google AI", "🔍 Google AI新闻")
        ]
        
        total_results = 0
        successful_searches = 0
        
        for i, (query, description) in enumerate(news_queries, 1):
            print(f"\n📰 新闻测试 {i}/{len(news_queries)}: {description}")
            print(f"关键词: '{query}'")
            print("-" * 50)
            
            try:
                import time
                start_time = time.time()
                
                # 执行新闻搜索
                result = search_service.search(query)
                
                end_time = time.time()
                search_duration = end_time - start_time
                
                # 添加请求间隔
                if i < len(news_queries):
                    print("⏳ 等待2秒以避免API频率限制...")
                    time.sleep(2)
                
                # 解析结果
                if isinstance(result, str):
                    if "错误" in result or "Error" in result:
                        print(f"❌ 搜索失败: {result}")
                    else:
                        try:
                            data = json.loads(result)
                            results = data.get("results", [])
                            
                            if results:
                                successful_searches += 1
                                total_results += len(results)
                                
                                print(f"✅ 新闻搜索成功！")
                                print(f"⏱️ 搜索耗时: {search_duration:.2f} 秒")
                                print(f"📊 找到 {len(results)} 条新闻")
                                print()
                                
                                # 显示详细新闻结果
                                for j, item in enumerate(results[:3], 1):  # 显示前3条
                                    print(f"📰 新闻 {j}:")
                                    print(f"   📰 标题: {item.get('title', 'N/A')}")
                                    print(f"   🔗 链接: {item.get('link', 'N/A')}")
                                    print(f"   📝 摘要: {item.get('snippet', 'N/A')[:120]}...")
                                    
                                    # 显示时间信息
                                    time_info = item.get('time_info', {})
                                    if time_info and time_info.get('formatted_time') != '未知时间':
                                        print(f"   ⏰ 时间: {time_info.get('formatted_time', 'N/A')} ({time_info.get('relative_time', 'N/A')})")
                                    else:
                                        print(f"   ⏰ 时间: 未知")
                                    
                                    # 显示新闻特有信息
                                    extra_info = []
                                    if item.get('news_type'):
                                        extra_info.append(f"类型: {item.get('news_type')}")
                                    if item.get('source'):
                                        extra_info.append(f"来源: {item.get('source')}")
                                    if item.get('thumbnail'):
                                        extra_info.append("有缩略图")
                                    if item.get('page_age'):
                                        extra_info.append(f"页面时间: {item.get('page_age')}")
                                    
                                    if extra_info:
                                        print(f"   ℹ️ 新闻信息: {'; '.join(extra_info)}")
                                    
                                    print()
                                
                                # 如果有更多结果，显示统计
                                if len(results) > 3:
                                    print(f"   ... 还有 {len(results) - 3} 条新闻未显示")
                                    
                            else:
                                print(f"❌ 未找到新闻结果")
                                print(f"⏱️ 搜索耗时: {search_duration:.2f} 秒")
                                
                        except json.JSONDecodeError as e:
                            print(f"❌ JSON解析错误: {e}")
                            print(f"原始结果: {result[:200]}...")
                else:
                    print(f"❌ 搜索返回了非字符串结果: {type(result)}")
                    
            except Exception as e:
                print(f"❌ 新闻搜索出错: {str(e)}")
                import traceback
                print(f"详细错误: {traceback.format_exc()}")
            
            print("-" * 50)
        
        # 显示新闻搜索统计
        print(f"\n📊 新闻搜索统计总结:")
        print("=" * 40)
        print(f"✅ 成功搜索: {successful_searches}/{len(news_queries)}")
        print(f"📰 总新闻数: {total_results}")
        print(f"📈 平均每次搜索新闻数: {total_results/max(successful_searches, 1):.1f}")
        print(f"🎯 成功率: {(successful_searches/len(news_queries)*100):.1f}%")
        
        print(f"\n💡 新闻搜索特点:")
        print("   📰 专门搜索新闻内容")
        print("   ⏰ 提供准确的发布时间")
        print("   🏢 显示新闻来源网站")
        print("   🖼️ 包含新闻缩略图")
        print("   🔍 更适合获取最新资讯")
        
    finally:
        # 恢复原始设置
        if original_search_type:
            os.environ['BRAVE_SEARCH_TYPE'] = original_search_type
        else:
            os.environ.pop('BRAVE_SEARCH_TYPE', None)
    
    print(f"\n🎉 新闻搜索测试完成！")
    print("=" * 60)

def compare_web_vs_news():
    """比较网页搜索和新闻搜索的结果"""
    print("\n⚖️ 网页搜索 vs 新闻搜索对比")
    print("=" * 60)
    
    query = "artificial intelligence"
    print(f"测试查询: '{query}'")
    
    search_types = [
        ("web", "🌐 网页搜索"),
        ("news", "📰 新闻搜索")
    ]
    
    for search_type, name in search_types:
        print(f"\n{name}:")
        print("-" * 30)
        
        # 临时设置搜索类型
        original_search_type = os.getenv('BRAVE_SEARCH_TYPE')
        os.environ['BRAVE_SEARCH_TYPE'] = search_type
        
        try:
            search_service = SearchService()
            result = search_service.search(query)
            
            try:
                data = json.loads(result)
                results = data.get("results", [])
                
                if results:
                    print(f"   ✅ 找到 {len(results)} 条结果")
                    # 显示第一个结果的标题
                    first_title = results[0].get('title', 'N/A')[:60]
                    print(f"   📄 首个结果: {first_title}...")
                    
                    # 显示特有信息
                    first_result = results[0]
                    if search_type == 'news':
                        if first_result.get('source'):
                            print(f"   🏢 新闻来源: {first_result.get('source')}")
                        if first_result.get('news_type'):
                            print(f"   📰 新闻类型: {first_result.get('news_type')}")
                    else:
                        if first_result.get('language'):
                            print(f"   🌐 语言: {first_result.get('language')}")
                        if first_result.get('profile'):
                            profile = str(first_result.get('profile'))[:50]
                            print(f"   🏢 网站简介: {profile}...")
                else:
                    print(f"   ❌ 未找到结果")
                    
            except json.JSONDecodeError:
                print(f"   ❌ 解析失败")
                
        except Exception as e:
            print(f"   ❌ 搜索失败: {str(e)}")
        
        finally:
            # 恢复原始设置
            if original_search_type:
                os.environ['BRAVE_SEARCH_TYPE'] = original_search_type
            else:
                os.environ.pop('BRAVE_SEARCH_TYPE', None)
    
    print(f"\n📝 对比总结:")
    print("   🌐 网页搜索: 适合获取全面的网页信息，包括教程、文档、博客等")
    print("   📰 新闻搜索: 适合获取最新资讯，包含准确的时间和来源信息")

if __name__ == "__main__":
    test_brave_news_search()
    compare_web_vs_news()
