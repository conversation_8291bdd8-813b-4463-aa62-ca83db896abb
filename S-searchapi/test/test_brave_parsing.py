#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Brave搜索解析优化测试脚本
测试根据官方文档优化后的解析功能
"""

import os
import json
from dotenv import load_dotenv
from demo import SearchService

# 加载环境变量
load_dotenv()

def test_web_search_parsing():
    """测试网页搜索解析功能"""
    print("🌐 测试Brave网页搜索解析功能")
    print("=" * 60)
    
    # 确保使用网页搜索模式
    original_search_type = os.getenv('BRAVE_SEARCH_TYPE')
    os.environ['BRAVE_SEARCH_TYPE'] = 'web'
    
    try:
        search_service = SearchService()
        
        # 测试查询
        test_query = "artificial intelligence"
        print(f"🔍 测试查询: '{test_query}'")
        print("-" * 40)
        
        # 执行搜索
        result = search_service.search(test_query)
        
        # 解析结果
        if isinstance(result, str):
            try:
                data = json.loads(result)
                results = data.get("results", [])
                
                if results:
                    print(f"✅ 成功获取 {len(results)} 个结果")
                    
                    # 分析第一个结果的详细信息
                    first_result = results[0]
                    print(f"\n📄 第一个结果详细分析:")
                    print(f"   标题: {first_result.get('title', 'N/A')}")
                    print(f"   链接: {first_result.get('link', 'N/A')}")
                    print(f"   类型: {first_result.get('type', 'N/A')} / {first_result.get('subtype', 'N/A')}")
                    
                    # 检查时间信息
                    time_info = first_result.get('time_info', {})
                    if time_info:
                        print(f"   时间: {time_info.get('formatted_time', 'N/A')} ({time_info.get('relative_time', 'N/A')})")
                    
                    # 检查Profile信息
                    if first_result.get('profile'):
                        profile = first_result.get('profile')
                        if isinstance(profile, dict):
                            print(f"   网站: {profile.get('name', 'N/A')}")
                        else:
                            print(f"   网站简介: {str(profile)[:50]}...")
                    
                    # 检查其他字段
                    fields_to_check = [
                        'language', 'family_friendly', 'is_live', 
                        'meta_url', 'thumbnail', 'deep_results', 
                        'rating', 'location'
                    ]
                    
                    available_fields = []
                    for field in fields_to_check:
                        if first_result.get(field):
                            available_fields.append(field)
                    
                    if available_fields:
                        print(f"   可用字段: {', '.join(available_fields)}")
                    
                    # 统计所有结果的字段覆盖率
                    print(f"\n📊 字段覆盖率统计:")
                    field_counts = {}
                    for result in results:
                        for field in fields_to_check:
                            if result.get(field):
                                field_counts[field] = field_counts.get(field, 0) + 1
                    
                    for field, count in field_counts.items():
                        percentage = (count / len(results)) * 100
                        print(f"   {field}: {count}/{len(results)} ({percentage:.1f}%)")
                
                else:
                    print("❌ 未找到搜索结果")
                    
            except json.JSONDecodeError as e:
                print(f"❌ JSON解析错误: {e}")
        else:
            print(f"❌ 搜索返回了非字符串结果: {type(result)}")
            
    finally:
        # 恢复原始设置
        if original_search_type:
            os.environ['BRAVE_SEARCH_TYPE'] = original_search_type
        else:
            os.environ.pop('BRAVE_SEARCH_TYPE', None)

def test_news_search_parsing():
    """测试新闻搜索解析功能"""
    print(f"\n📰 测试Brave新闻搜索解析功能")
    print("=" * 60)
    
    # 确保使用新闻搜索模式
    original_search_type = os.getenv('BRAVE_SEARCH_TYPE')
    os.environ['BRAVE_SEARCH_TYPE'] = 'news'
    
    try:
        search_service = SearchService()
        
        # 测试查询
        test_query = "veo3"
        print(f"📰 测试查询: '{test_query}'")
        print("-" * 40)
        
        # 执行搜索
        result = search_service.search(test_query)
        
        # 解析结果
        if isinstance(result, str):
            try:
                data = json.loads(result)
                results = data.get("results", [])
                
                if results:
                    print(f"✅ 成功获取 {len(results)} 个新闻结果")
                    
                    # 分析第一个结果的详细信息
                    first_result = results[0]
                    print(f"\n📰 第一个新闻详细分析:")
                    print(f"   标题: {first_result.get('title', 'N/A')}")
                    print(f"   链接: {first_result.get('link', 'N/A')}")
                    print(f"   类型: {first_result.get('type', 'N/A')}")
                    
                    # 检查时间信息
                    time_info = first_result.get('time_info', {})
                    if time_info:
                        print(f"   时间: {time_info.get('formatted_time', 'N/A')} ({time_info.get('relative_time', 'N/A')})")
                    
                    # 检查新闻特有字段
                    news_fields = [
                        'source', 'netloc', 'breaking', 'is_live', 
                        'thumbnail', 'thumbnail_url', 'extra_snippets',
                        'language', 'family_friendly'
                    ]
                    
                    available_fields = []
                    for field in news_fields:
                        if first_result.get(field):
                            value = first_result.get(field)
                            if field == 'breaking' and value:
                                available_fields.append(f"{field}(突发)")
                            elif field == 'is_live' and value:
                                available_fields.append(f"{field}(实时)")
                            elif field == 'extra_snippets':
                                available_fields.append(f"{field}({len(value)})")
                            else:
                                available_fields.append(field)
                    
                    if available_fields:
                        print(f"   新闻字段: {', '.join(available_fields)}")
                    
                    # 统计所有新闻结果的字段覆盖率
                    print(f"\n📊 新闻字段覆盖率统计:")
                    field_counts = {}
                    for result in results:
                        for field in news_fields:
                            if result.get(field):
                                field_counts[field] = field_counts.get(field, 0) + 1
                    
                    for field, count in field_counts.items():
                        percentage = (count / len(results)) * 100
                        print(f"   {field}: {count}/{len(results)} ({percentage:.1f}%)")
                
                else:
                    print("❌ 未找到新闻结果")
                    
            except json.JSONDecodeError as e:
                print(f"❌ JSON解析错误: {e}")
        else:
            print(f"❌ 搜索返回了非字符串结果: {type(result)}")
            
    finally:
        # 恢复原始设置
        if original_search_type:
            os.environ['BRAVE_SEARCH_TYPE'] = original_search_type
        else:
            os.environ.pop('BRAVE_SEARCH_TYPE', None)

def test_response_structure():
    """测试响应结构解析"""
    print(f"\n🔍 测试Brave API响应结构解析")
    print("=" * 60)
    
    # 使用网页搜索测试完整响应结构
    os.environ['BRAVE_SEARCH_TYPE'] = 'web'
    
    try:
        search_service = SearchService()
        
        # 执行一次搜索来获取完整响应
        result = search_service.search("technology")
        
        if isinstance(result, str):
            try:
                data = json.loads(result)
                
                print("📋 API响应结构分析:")
                
                # 检查顶级字段
                top_level_fields = ['type', 'query', 'web', 'news', 'videos', 'locations', 'infobox', 'mixed']
                available_top_fields = []
                
                for field in top_level_fields:
                    if field in data:
                        if field == 'web' and data[field].get('results'):
                            available_top_fields.append(f"{field}({len(data[field]['results'])})")
                        elif field == 'news' and data[field].get('results'):
                            available_top_fields.append(f"{field}({len(data[field]['results'])})")
                        elif field == 'videos' and data[field].get('results'):
                            available_top_fields.append(f"{field}({len(data[field]['results'])})")
                        elif field == 'locations' and data[field].get('results'):
                            available_top_fields.append(f"{field}({len(data[field]['results'])})")
                        else:
                            available_top_fields.append(field)
                
                print(f"   顶级字段: {', '.join(available_top_fields)}")
                
                # 分析查询信息
                if 'query' in data:
                    query_info = data['query']
                    query_fields = []
                    for key, value in query_info.items():
                        if value:
                            query_fields.append(f"{key}={value}")
                    print(f"   查询信息: {'; '.join(query_fields[:5])}...")  # 只显示前5个
                
                print(f"\n✅ 响应结构解析完成")
                
            except json.JSONDecodeError as e:
                print(f"❌ JSON解析错误: {e}")
        else:
            print(f"❌ 搜索返回了非字符串结果: {type(result)}")
            
    except Exception as e:
        print(f"❌ 测试出错: {str(e)}")

def main():
    """主测试函数"""
    print("🧪 Brave搜索解析优化测试")
    print("基于官方API文档优化解析功能")
    print("=" * 80)
    
    # 检查API Token
    brave_token = os.getenv('BRAVE_API_TOKEN')
    if not brave_token or brave_token == 'your_brave_api_token_here':
        print("❌ 错误：未配置BRAVE_API_TOKEN")
        return
    
    print(f"✅ API Token: {brave_token[:10]}...{brave_token[-5:]}")
    
    # 运行测试
    test_web_search_parsing()
    test_news_search_parsing()
    test_response_structure()
    
    print(f"\n🎉 解析优化测试完成！")
    print("=" * 80)
    
    print(f"\n💡 优化总结:")
    print("   🌐 网页搜索: 支持完整的SearchResult字段解析")
    print("   📰 新闻搜索: 支持NewsResult特有字段解析")
    print("   🔍 响应结构: 支持完整的API响应结构")
    print("   ⏰ 时间解析: 支持age、page_age、page_fetched字段")
    print("   📊 字段覆盖: 提供详细的字段覆盖率统计")

if __name__ == "__main__":
    main()
