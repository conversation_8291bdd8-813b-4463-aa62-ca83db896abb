#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Brave搜索单次测试脚本
用于验证配置修复和API连接
"""

import os
import json
from dotenv import load_dotenv
from demo import SearchService

# 加载环境变量
load_dotenv()

def test_single_search():
    """测试单次搜索"""
    print("🔍 Brave搜索单次测试")
    print("=" * 50)
    
    # 初始化搜索服务
    search_service = SearchService()
    
    # 检查配置
    print("⚙️ 配置检查:")
    print(f"   搜索服务: {search_service.search_service}")
    print(f"   最大结果数: {search_service.max_results}")
    
    # 检查Brave配置
    brave_token = os.getenv('BRAVE_API_TOKEN')
    brave_search_lang = os.getenv('BRAVE_SEARCH_LANG', 'zh-hans')
    
    print(f"\n🦁 Brave配置:")
    if brave_token and brave_token != 'your_brave_api_token_here':
        print(f"   ✅ API Token: {brave_token[:10]}...{brave_token[-5:]}")
    else:
        print(f"   ❌ API Token: 未配置")
        return
    
    print(f"   搜索语言: {brave_search_lang}")
    print(f"   国家代码: {os.getenv('BRAVE_COUNTRY', 'CN')}")
    
    # 执行单次搜索测试
    test_query = "artificial intelligence"  # 使用英文查询避免语言问题
    
    print(f"\n🔍 测试搜索:")
    print(f"关键词: '{test_query}'")
    print("-" * 30)
    
    try:
        import time
        start_time = time.time()
        
        # 执行搜索
        result = search_service.search(test_query)
        
        end_time = time.time()
        search_duration = end_time - start_time
        
        print(f"⏱️ 搜索耗时: {search_duration:.2f} 秒")
        
        # 解析结果
        if isinstance(result, str):
            if "错误" in result or "Error" in result:
                print(f"❌ 搜索失败: {result}")
            else:
                try:
                    data = json.loads(result)
                    results = data.get("results", [])
                    
                    if results:
                        print(f"✅ 搜索成功！找到 {len(results)} 条结果")
                        
                        # 显示第一个结果
                        first_result = results[0]
                        print(f"\n📄 第一个结果:")
                        print(f"   标题: {first_result.get('title', 'N/A')}")
                        print(f"   链接: {first_result.get('link', 'N/A')}")
                        print(f"   摘要: {first_result.get('snippet', 'N/A')[:100]}...")
                        
                        # 显示时间信息
                        time_info = first_result.get('time_info', {})
                        if time_info and time_info.get('formatted_time') != '未知时间':
                            print(f"   时间: {time_info.get('formatted_time', 'N/A')} ({time_info.get('relative_time', 'N/A')})")
                        
                        # 显示Brave特有信息
                        if first_result.get('profile'):
                            print(f"   网站简介: {first_result.get('profile')[:50]}...")
                        if first_result.get('language'):
                            print(f"   语言: {first_result.get('language')}")
                            
                    else:
                        print("❌ 未找到搜索结果")
                        
                except json.JSONDecodeError as e:
                    print(f"❌ JSON解析错误: {e}")
                    print(f"原始结果: {result[:200]}...")
        else:
            print(f"❌ 搜索返回了非字符串结果: {type(result)}")
            
    except Exception as e:
        print(f"❌ 搜索出错: {str(e)}")
        import traceback
        print(f"详细错误: {traceback.format_exc()}")
    
    print("\n" + "=" * 50)
    print("🎉 单次测试完成")

if __name__ == "__main__":
    test_single_search()
