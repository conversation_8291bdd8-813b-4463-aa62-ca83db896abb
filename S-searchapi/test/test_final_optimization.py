#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Brave搜索最终优化验证脚本
验证所有根据官方文档优化的功能
"""

import os
import json
import time
from dotenv import load_dotenv
from demo import SearchService

# 加载环境变量
load_dotenv()

def test_comprehensive_parsing():
    """综合测试解析功能"""
    print("🔍 Brave搜索综合解析测试")
    print("=" * 60)
    
    # 测试网页搜索
    print("🌐 网页搜索测试:")
    print("-" * 30)
    
    os.environ['BRAVE_SEARCH_TYPE'] = 'web'
    search_service = SearchService()
    
    # 执行搜索
    result = search_service.search("python programming")
    
    if isinstance(result, str):
        try:
            data = json.loads(result)
            results = data.get("results", [])
            
            if results:
                print(f"✅ 获取 {len(results)} 个网页结果")
                
                # 分析字段覆盖率
                field_stats = {
                    'profile': 0, 'language': 0, 'family_friendly': 0,
                    'meta_url': 0, 'thumbnail': 0, 'age': 0,
                    'deep_results': 0, 'rating': 0
                }
                
                for result_item in results:
                    for field in field_stats:
                        if result_item.get(field):
                            field_stats[field] += 1
                
                print("📊 字段覆盖率:")
                for field, count in field_stats.items():
                    percentage = (count / len(results)) * 100
                    print(f"   {field}: {percentage:.1f}%")
                
                # 显示第一个结果的详细信息
                first = results[0]
                print(f"\n📄 示例结果:")
                print(f"   标题: {first.get('title', 'N/A')[:60]}...")
                print(f"   类型: {first.get('type', 'N/A')}/{first.get('subtype', 'N/A')}")
                
                time_info = first.get('time_info', {})
                if time_info:
                    print(f"   时间: {time_info.get('relative_time', 'N/A')}")
                
                if first.get('profile'):
                    profile = first.get('profile')
                    if isinstance(profile, dict):
                        print(f"   网站: {profile.get('name', 'N/A')}")
                
            else:
                print("❌ 未获取到网页结果")
                
        except json.JSONDecodeError:
            print("❌ JSON解析失败")
    
    # 等待避免频率限制
    print("\n⏳ 等待3秒...")
    time.sleep(3)
    
    # 测试新闻搜索
    print("\n📰 新闻搜索测试:")
    print("-" * 30)
    
    os.environ['BRAVE_SEARCH_TYPE'] = 'news'
    search_service = SearchService()
    
    # 执行新闻搜索
    result = search_service.search("technology news")
    
    if isinstance(result, str):
        try:
            data = json.loads(result)
            results = data.get("results", [])
            
            if results:
                print(f"✅ 获取 {len(results)} 个新闻结果")
                
                # 分析新闻特有字段
                news_field_stats = {
                    'source': 0, 'breaking': 0, 'is_live': 0,
                    'thumbnail': 0, 'extra_snippets': 0, 'age': 0
                }
                
                for result_item in results:
                    for field in news_field_stats:
                        if result_item.get(field):
                            news_field_stats[field] += 1
                
                print("📊 新闻字段覆盖率:")
                for field, count in news_field_stats.items():
                    percentage = (count / len(results)) * 100
                    print(f"   {field}: {percentage:.1f}%")
                
                # 显示第一个新闻的详细信息
                first_news = results[0]
                print(f"\n📰 示例新闻:")
                print(f"   标题: {first_news.get('title', 'N/A')[:60]}...")
                print(f"   类型: {first_news.get('type', 'N/A')}")
                
                if first_news.get('source'):
                    print(f"   来源: {first_news.get('source')}")
                elif first_news.get('netloc'):
                    print(f"   域名: {first_news.get('netloc')}")
                
                time_info = first_news.get('time_info', {})
                if time_info:
                    print(f"   时间: {time_info.get('relative_time', 'N/A')}")
                
                if first_news.get('breaking'):
                    print(f"   🚨 突发新闻")
                if first_news.get('is_live'):
                    print(f"   🔴 实时新闻")
                
            else:
                print("❌ 未获取到新闻结果")
                
        except json.JSONDecodeError:
            print("❌ JSON解析失败")

def test_time_parsing():
    """测试时间解析功能"""
    print(f"\n⏰ 时间解析功能测试")
    print("=" * 60)
    
    from demo import SearchService
    service = SearchService()
    
    # 测试各种时间格式
    test_times = [
        "17 hours ago",
        "4 days ago", 
        "1 week ago",
        "2 weeks ago",
        "1 month ago",
        "May 13, 2024",
        "June 28, 2024",
        "September 21, 2023",
        "2025-05-26T12:00:00Z",
        "刚刚",
        "3小时前",
        "2天前"
    ]
    
    print("🕐 测试时间格式解析:")
    for time_str in test_times:
        time_info = service._format_time(time_str, "brave")
        formatted = time_info.get('formatted_time', 'N/A')
        relative = time_info.get('relative_time', 'N/A')
        print(f"   '{time_str}' → {formatted} ({relative})")

def test_response_structure():
    """测试响应结构解析"""
    print(f"\n🏗️ 响应结构解析测试")
    print("=" * 60)
    
    os.environ['BRAVE_SEARCH_TYPE'] = 'web'
    search_service = SearchService()
    
    # 执行搜索获取完整响应
    result = search_service.search("machine learning")
    
    if isinstance(result, str):
        try:
            data = json.loads(result)
            
            print("📋 API响应结构分析:")
            
            # 检查顶级字段
            top_fields = ['type', 'query', 'web', 'news', 'videos', 'locations', 'infobox']
            present_fields = []
            
            for field in top_fields:
                if field in data:
                    if field in ['web', 'news', 'videos', 'locations'] and data[field].get('results'):
                        count = len(data[field]['results'])
                        present_fields.append(f"{field}({count})")
                    else:
                        present_fields.append(field)
            
            print(f"   顶级字段: {', '.join(present_fields)}")
            
            # 分析查询信息
            if 'query' in data:
                query_info = data['query']
                query_details = []
                
                important_query_fields = ['original', 'altered', 'is_navigational', 'is_geolocal', 'language']
                for field in important_query_fields:
                    if field in query_info and query_info[field]:
                        query_details.append(f"{field}={query_info[field]}")
                
                if query_details:
                    print(f"   查询信息: {'; '.join(query_details[:3])}...")
            
            # 分析网页结果结构
            if 'web' in data and data['web'].get('results'):
                web_results = data['web']['results']
                first_result = web_results[0]
                
                result_fields = []
                important_result_fields = ['type', 'subtype', 'age', 'language', 'family_friendly']
                for field in important_result_fields:
                    if field in first_result:
                        result_fields.append(f"{field}={first_result[field]}")
                
                print(f"   结果字段: {'; '.join(result_fields[:4])}...")
            
            print(f"✅ 响应结构解析完成")
            
        except json.JSONDecodeError:
            print("❌ JSON解析失败")

def main():
    """主测试函数"""
    print("🧪 Brave搜索最终优化验证")
    print("基于官方API文档的完整解析功能测试")
    print("=" * 80)
    
    # 检查配置
    brave_token = os.getenv('BRAVE_API_TOKEN')
    if not brave_token or brave_token == 'your_brave_api_token_here':
        print("❌ 错误：未配置BRAVE_API_TOKEN")
        return
    
    print(f"✅ API Token: {brave_token[:10]}...{brave_token[-5:]}")
    print(f"✅ 搜索语言: {os.getenv('BRAVE_SEARCH_LANG', 'zh-hans')}")
    print(f"✅ 国家代码: {os.getenv('BRAVE_COUNTRY', 'CN')}")
    
    # 运行所有测试
    test_comprehensive_parsing()
    test_time_parsing()
    test_response_structure()
    
    print(f"\n🎉 最终优化验证完成！")
    print("=" * 80)
    
    print(f"\n📊 优化成果总结:")
    print("✅ 完整的SearchResult字段解析 (type, subtype, age, profile, meta_url等)")
    print("✅ 完整的NewsResult字段解析 (source, breaking, is_live, thumbnail等)")
    print("✅ 增强的时间解析 (支持多种英文和中文时间格式)")
    print("✅ 详细的响应结构分析 (query, web, news, videos等)")
    print("✅ 字段覆盖率统计 (提供详细的字段使用情况)")
    print("✅ 错误处理优化 (更友好的错误信息和降级处理)")
    
    print(f"\n🔧 技术特性:")
    print("📋 基于Brave Search API官方文档v1.0")
    print("🌐 支持Web搜索和News搜索两种模式")
    print("⏰ 智能时间解析和相对时间计算")
    print("🛡️ 完善的API频率限制处理")
    print("📊 详细的调试信息和统计数据")
    
    # 恢复原始设置
    os.environ.pop('BRAVE_SEARCH_TYPE', None)

if __name__ == "__main__":
    main()
