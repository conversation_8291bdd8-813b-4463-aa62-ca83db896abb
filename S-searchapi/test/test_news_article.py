#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
新闻文章生成器测试脚本
测试各个功能模块的正确性
"""

import os
import json
import unittest
from unittest.mock import patch, MagicMock
from dotenv import load_dotenv
from news_article_generator import NewsArticleGenerator

# 加载环境变量
load_dotenv()


class TestNewsArticleGenerator(unittest.TestCase):
    """新闻文章生成器测试类"""
    
    def setUp(self):
        """测试前准备"""
        self.generator = NewsArticleGenerator()
    
    def test_init(self):
        """测试初始化"""
        self.assertIsNotNone(self.generator.search_service)
        self.assertIsNotNone(self.generator.ai_service)
        self.assertIsInstance(self.generator.max_news_count, int)
        self.assertGreater(self.generator.max_news_count, 0)
    
    def test_get_ai_service(self):
        """测试AI服务检测"""
        # 测试无配置情况
        with patch.dict(os.environ, {}, clear=True):
            generator = NewsArticleGenerator()
            self.assertEqual(generator.ai_service, 'demo')
        
        # 测试OpenAI配置
        with patch.dict(os.environ, {'OPENAI_API_KEY': 'test_key'}):
            generator = NewsArticleGenerator()
            self.assertEqual(generator.ai_service, 'openai')
    
    @patch('news_article_generator.SearchService')
    def test_search_news(self, mock_search_service):
        """测试新闻搜索功能"""
        # 模拟搜索结果
        mock_result = {
            'results': [
                {
                    'title': '测试新闻1',
                    'link': 'https://example.com/1',
                    'snippet': '这是测试新闻1的摘要',
                    'time_info': {'formatted_time': '2024-01-01'},
                    'source': '测试来源1'
                },
                {
                    'title': '测试新闻2',
                    'link': 'https://example.com/2',
                    'snippet': '这是测试新闻2的摘要',
                    'time_info': {'formatted_time': '2024-01-02'},
                    'source': '测试来源2'
                }
            ]
        }
        
        # 配置mock
        mock_search_instance = MagicMock()
        mock_search_instance.search.return_value = json.dumps(mock_result)
        mock_search_service.return_value = mock_search_instance
        
        # 创建新的生成器实例
        generator = NewsArticleGenerator()
        generator.search_service = mock_search_instance
        
        # 测试搜索
        result = generator.search_news("测试关键词")
        
        # 验证结果
        self.assertEqual(len(result), 2)
        self.assertEqual(result[0]['title'], '测试新闻1')
        self.assertEqual(result[1]['title'], '测试新闻2')
    
    def test_integrate_news_content(self):
        """测试新闻内容整合"""
        # 测试空列表
        result = self.generator.integrate_news_content([])
        self.assertIn("未找到相关新闻内容", result)
        
        # 测试正常新闻列表
        news_list = [
            {
                'index': 1,
                'title': '测试新闻1',
                'link': 'https://example.com/1',
                'snippet': '测试摘要1',
                'time_info': {'formatted_time': '2024-01-01'},
                'source': '测试来源1'
            },
            {
                'index': 2,
                'title': '测试新闻2',
                'link': 'https://example.com/2',
                'snippet': '测试摘要2',
                'time_info': {'formatted_time': '2024-01-02'},
                'source': '测试来源2'
            }
        ]
        
        result = self.generator.integrate_news_content(news_list)
        
        # 验证结果包含所有新闻信息
        self.assertIn('测试新闻1', result)
        self.assertIn('测试新闻2', result)
        self.assertIn('测试摘要1', result)
        self.assertIn('测试摘要2', result)
        self.assertIn('总计：2 条新闻', result)
    
    def test_generate_article_prompt(self):
        """测试文章提示词生成"""
        keyword = "人工智能"
        news_content = "测试新闻内容"
        
        prompt = self.generator.generate_article_prompt(keyword, news_content)
        
        # 验证提示词包含关键信息
        self.assertIn(keyword, prompt)
        self.assertIn(news_content, prompt)
        self.assertIn("公众号文章", prompt)
        self.assertIn("写作要求", prompt)
    
    def test_call_demo_ai(self):
        """测试演示AI功能"""
        prompt = "测试提示词"
        result = self.generator._call_demo_ai(prompt)
        
        # 验证返回结果
        self.assertIsInstance(result, str)
        self.assertGreater(len(result), 100)  # 文章应该有一定长度
        self.assertIn("人工智能", result)  # 演示文章应该包含AI相关内容
    
    @patch('news_article_generator.SearchService')
    def test_generate_article_success(self, mock_search_service):
        """测试完整文章生成流程 - 成功情况"""
        # 模拟搜索结果
        mock_result = {
            'results': [
                {
                    'title': '测试新闻',
                    'link': 'https://example.com',
                    'snippet': '测试摘要',
                    'time_info': {'formatted_time': '2024-01-01'},
                    'source': '测试来源'
                }
            ]
        }
        
        # 配置mock
        mock_search_instance = MagicMock()
        mock_search_instance.search.return_value = json.dumps(mock_result)
        mock_search_service.return_value = mock_search_instance
        
        # 创建生成器并设置为演示模式
        generator = NewsArticleGenerator()
        generator.search_service = mock_search_instance
        generator.ai_service = 'demo'
        
        # 测试生成文章
        result = generator.generate_article("测试关键词")
        
        # 验证结果
        self.assertTrue(result['success'])
        self.assertEqual(result['keyword'], "测试关键词")
        self.assertEqual(result['news_count'], 1)
        self.assertGreater(result['article_length'], 0)
        self.assertIn('article_content', result)
    
    @patch('news_article_generator.SearchService')
    def test_generate_article_no_news(self, mock_search_service):
        """测试完整文章生成流程 - 无新闻情况"""
        # 模拟无搜索结果
        mock_result = {'results': []}
        
        # 配置mock
        mock_search_instance = MagicMock()
        mock_search_instance.search.return_value = json.dumps(mock_result)
        mock_search_service.return_value = mock_search_instance
        
        # 创建生成器
        generator = NewsArticleGenerator()
        generator.search_service = mock_search_instance
        
        # 测试生成文章
        result = generator.generate_article("测试关键词")
        
        # 验证结果
        self.assertFalse(result['success'])
        self.assertIn('未找到相关新闻', result['error'])
    
    def test_save_article(self):
        """测试文章保存功能"""
        # 测试成功结果
        success_result = {
            'success': True,
            'keyword': '测试关键词',
            'article_content': '测试文章内容',
            'news_count': 1,
            'article_length': 100,
            'ai_service': 'demo',
            'elapsed_time': 1.5,
            'timestamp': '2024-01-01T12:00:00',
            'integrated_content': '测试新闻内容'
        }
        
        # 测试保存
        filepath = self.generator.save_article(success_result, output_dir="test_output")
        
        # 验证文件存在
        self.assertTrue(os.path.exists(filepath))
        
        # 验证文件内容
        with open(filepath, 'r', encoding='utf-8') as f:
            content = f.read()
            self.assertIn('测试关键词', content)
            self.assertIn('测试文章内容', content)
        
        # 清理测试文件
        if os.path.exists(filepath):
            os.remove(filepath)
        
        # 测试失败结果
        fail_result = {'success': False}
        filepath = self.generator.save_article(fail_result)
        self.assertEqual(filepath, "")


def run_integration_test():
    """运行集成测试"""
    print("🧪 运行集成测试")
    print("=" * 50)
    
    try:
        # 创建生成器
        generator = NewsArticleGenerator()
        
        # 测试基本功能
        print("🔄 测试基本文章生成功能...")
        result = generator.generate_article("人工智能测试")
        
        if result['success']:
            print(f"✅ 集成测试成功!")
            print(f"   关键词: {result['keyword']}")
            print(f"   新闻数量: {result['news_count']}")
            print(f"   文章长度: {result['article_length']} 字符")
            print(f"   AI服务: {result['ai_service']}")
            print(f"   耗时: {result['elapsed_time']} 秒")
            
            # 保存测试文章
            filepath = generator.save_article(result, output_dir="integration_test")
            if filepath:
                print(f"   测试文章已保存: {filepath}")
        else:
            print(f"❌ 集成测试失败: {result['error']}")
            
    except Exception as e:
        print(f"💥 集成测试异常: {str(e)}")


def main():
    """主函数"""
    print("🧪 新闻文章生成器测试套件")
    print("=" * 60)
    
    # 运行单元测试
    print("📋 运行单元测试...")
    unittest.main(argv=[''], exit=False, verbosity=2)
    
    print("\n" + "=" * 60)
    
    # 运行集成测试
    run_integration_test()
    
    print(f"\n🎉 测试完成!")


if __name__ == "__main__":
    main()
