#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
新闻文章生成器测试脚本
测试优化后的新闻搜索、内容整合和AI文章生成功能
"""

import os
import sys
import json
from datetime import datetime
from dotenv import load_dotenv

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from news_article_generator import NewsArticleGenerator

# 加载环境变量
load_dotenv()


def test_news_generator():
    """测试新闻文章生成器"""
    print("🧪 新闻文章生成器测试")
    print("=" * 60)
    
    # 初始化生成器
    try:
        generator = NewsArticleGenerator()
        print(f"✅ 生成器初始化成功")
    except Exception as e:
        print(f"❌ 生成器初始化失败: {str(e)}")
        return False
    
    # 测试关键词列表
    test_keywords = [
        "人工智能",
        "新能源汽车", 
        "ChatGPT",
        "区块链",
        "元宇宙"
    ]
    
    print(f"\n🎯 可用测试关键词: {', '.join(test_keywords)}")
    
    # 让用户选择测试关键词
    try:
        print(f"\n请选择测试关键词:")
        for i, keyword in enumerate(test_keywords, 1):
            print(f"   {i}. {keyword}")
        print(f"   0. 自定义关键词")
        
        choice = input("\n请输入选择 (1-5 或 0): ").strip()
        
        if choice == '0':
            keyword = input("请输入自定义关键词: ").strip()
            if not keyword:
                keyword = "人工智能"  # 默认关键词
        elif choice.isdigit() and 1 <= int(choice) <= len(test_keywords):
            keyword = test_keywords[int(choice) - 1]
        else:
            keyword = test_keywords[0]  # 默认第一个
            
        print(f"\n🎯 选择的关键词: '{keyword}'")
        
        # 测试新闻搜索
        print(f"\n" + "="*60)
        print("📝 第一步：测试新闻搜索功能")
        print("="*60)
        
        news_list = generator.search_news(keyword)
        
        if not news_list:
            print("❌ 新闻搜索失败，无法继续测试")
            return False
            
        print(f"✅ 新闻搜索成功，找到 {len(news_list)} 条新闻")
        
        # 显示搜索结果摘要
        print(f"\n📊 搜索结果摘要:")
        for i, news in enumerate(news_list[:3], 1):  # 只显示前3条
            score = news.get('relevance_score', 0)
            time_str = news.get('time_info', {}).get('formatted_time', '未知时间')
            print(f"   {i}. {news['title'][:40]}... (评分: {score:.0f}, 时间: {time_str})")
        
        if len(news_list) > 3:
            print(f"   ... 还有 {len(news_list) - 3} 条新闻")
        
        # 测试内容整合
        print(f"\n" + "="*60)
        print("📝 第二步：测试新闻内容整合功能")
        print("="*60)
        
        integrated_content = generator.integrate_news_content(news_list)
        
        print(f"✅ 新闻内容整合完成")
        print(f"   整合内容长度: {len(integrated_content)} 字符")
        
        # 显示整合内容预览
        preview_length = 500
        print(f"\n📄 整合内容预览 (前{preview_length}字符):")
        print("-" * 50)
        print(integrated_content[:preview_length] + ("..." if len(integrated_content) > preview_length else ""))
        print("-" * 50)
        
        # 测试提示词生成
        print(f"\n" + "="*60)
        print("📝 第三步：测试AI提示词生成")
        print("="*60)
        
        prompt = generator.generate_article_prompt(keyword, integrated_content)
        
        print(f"✅ AI提示词生成完成")
        print(f"   提示词长度: {len(prompt)} 字符")
        
        # 显示提示词预览
        prompt_preview_length = 300
        print(f"\n📄 提示词预览 (前{prompt_preview_length}字符):")
        print("-" * 50)
        print(prompt[:prompt_preview_length] + ("..." if len(prompt) > prompt_preview_length else ""))
        print("-" * 50)
        
        # 询问是否继续AI生成测试
        continue_ai = input(f"\n是否继续测试AI文章生成？(y/n): ").strip().lower()
        
        if continue_ai in ['y', 'yes', '是', '']:
            # 测试AI文章生成
            print(f"\n" + "="*60)
            print("📝 第四步：测试AI文章生成功能")
            print("="*60)
            
            try:
                article_content = generator.call_ai_service(prompt)
                
                print(f"✅ AI文章生成完成")
                print(f"   文章长度: {len(article_content)} 字符")
                
                # 显示文章预览
                article_preview_length = 600
                print(f"\n📄 生成文章预览 (前{article_preview_length}字符):")
                print("-" * 50)
                print(article_content[:article_preview_length] + ("..." if len(article_content) > article_preview_length else ""))
                print("-" * 50)
                
                # 保存测试结果
                save_test = input(f"\n是否保存测试结果？(y/n): ").strip().lower()
                
                if save_test in ['y', 'yes', '是', '']:
                    # 构建完整结果
                    result = {
                        'success': True,
                        'keyword': keyword,
                        'article_content': article_content,
                        'news_count': len(news_list),
                        'news_list': news_list,
                        'integrated_content': integrated_content,
                        'ai_service': generator.ai_service,
                        'timestamp': datetime.now().isoformat(),
                        'article_length': len(article_content),
                        'test_mode': True
                    }
                    
                    filepath = generator.save_article(result, "test_output")
                    
                    if filepath:
                        print(f"✅ 测试结果已保存到: {filepath}")
                    
            except Exception as e:
                print(f"❌ AI文章生成失败: {str(e)}")
                print(f"   这可能是因为:")
                print(f"   1. 未配置AI服务API密钥")
                print(f"   2. API调用限制或网络问题")
                print(f"   3. AI服务暂时不可用")
                print(f"\n💡 建议:")
                print(f"   - 检查.env文件中的API密钥配置")
                print(f"   - 确认网络连接正常")
                print(f"   - 尝试使用演示模式")
                
                return False
        else:
            print(f"⏭️ 跳过AI文章生成测试")
        
        print(f"\n🎉 测试完成！")
        return True
        
    except KeyboardInterrupt:
        print(f"\n\n👋 用户中断测试")
        return False
    except Exception as e:
        print(f"\n❌ 测试过程中出错: {str(e)}")
        return False


def check_environment():
    """检查环境配置"""
    print("🔧 环境配置检查")
    print("-" * 40)
    
    # 检查搜索服务配置
    search_service = os.getenv('SEARCH_SERVICE')
    if search_service:
        print(f"✅ 搜索服务: {search_service}")
    else:
        print(f"⚠️ 未配置搜索服务 (SEARCH_SERVICE)")
        
    # 检查AI服务配置
    ai_services = {
        'OpenAI': os.getenv('OPENAI_API_KEY'),
        'Claude': os.getenv('CLAUDE_API_KEY'),
        'Groq': os.getenv('GROQ_API_KEY'),
        'Moonshot': os.getenv('MOONSHOT_API_KEY'),
        'DeepSeek': os.getenv('DEEPSEEK_API_KEY')
    }
    
    configured_ai = []
    for service, key in ai_services.items():
        if key:
            configured_ai.append(service)
            
    if configured_ai:
        print(f"✅ 已配置AI服务: {', '.join(configured_ai)}")
    else:
        print(f"⚠️ 未配置AI服务API密钥")
        
    print()


if __name__ == "__main__":
    print("🧪 新闻文章生成器测试工具")
    print("=" * 60)
    
    # 检查环境
    check_environment()
    
    # 运行测试
    success = test_news_generator()
    
    if success:
        print(f"\n✅ 所有测试通过！")
    else:
        print(f"\n❌ 测试失败，请检查配置和错误信息")
