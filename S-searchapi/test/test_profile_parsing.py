#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Brave搜索Profile字段解析测试脚本
测试优化后的profile字段解析功能
"""

import os
import json
from dotenv import load_dotenv
from demo import SearchService

# 加载环境变量
load_dotenv()

def test_profile_parsing():
    """测试profile字段解析"""
    print("🔍 Brave搜索Profile字段解析测试")
    print("=" * 60)
    
    # 确保使用网页搜索模式
    os.environ['BRAVE_SEARCH_TYPE'] = 'web'
    
    search_service = SearchService()
    
    # 测试查询 - 选择可能有丰富profile信息的查询
    test_queries = [
        ("Google DeepMind", "🧠 AI研究机构"),
        ("OpenAI", "🤖 AI公司"),
        ("Microsoft", "💻 科技公司"),
        ("Wikipedia", "📚 知识百科"),
        ("GitHub", "👨‍💻 代码托管")
    ]
    
    total_profiles = 0
    dict_profiles = 0
    string_profiles = 0
    
    for i, (query, description) in enumerate(test_queries, 1):
        print(f"\n🔍 测试 {i}/5: {description}")
        print(f"查询: '{query}'")
        print("-" * 40)
        
        try:
            # 执行搜索
            result = search_service.search(query)
            
            if isinstance(result, str):
                try:
                    data = json.loads(result)
                    results = data.get("results", [])
                    
                    if results:
                        print(f"✅ 获取 {len(results)} 个结果")
                        
                        # 分析profile字段
                        for j, item in enumerate(results[:3], 1):  # 只分析前3个结果
                            print(f"\n📄 结果 {j}:")
                            print(f"   标题: {item.get('title', 'N/A')[:60]}...")
                            
                            profile = item.get('profile')
                            if profile:
                                total_profiles += 1
                                print(f"   🏢 Profile信息:")
                                
                                if isinstance(profile, dict):
                                    dict_profiles += 1
                                    print(f"      类型: 字典格式")
                                    
                                    # 显示所有可用的profile字段
                                    profile_fields = []
                                    if profile.get('name'):
                                        profile_fields.append(f"name='{profile.get('name')}'")
                                    if profile.get('long_name'):
                                        profile_fields.append(f"long_name='{profile.get('long_name')}'")
                                    if profile.get('url'):
                                        profile_fields.append(f"url='{profile.get('url')[:50]}...'")
                                    if profile.get('img'):
                                        profile_fields.append(f"img='{profile.get('img')[:50]}...'")
                                    
                                    print(f"      字段: {'; '.join(profile_fields)}")
                                    
                                    # 显示优化后的解析结果
                                    profile_name = profile.get('name', '')
                                    profile_long_name = profile.get('long_name', '')
                                    if profile_name:
                                        print(f"      ✅ 解析结果: 网站: {profile_name}")
                                    elif profile_long_name:
                                        print(f"      ✅ 解析结果: 网站: {profile_long_name}")
                                    else:
                                        print(f"      ⚠️ 解析结果: 无可用名称")
                                
                                else:
                                    string_profiles += 1
                                    profile_text = str(profile)
                                    print(f"      类型: 字符串格式")
                                    print(f"      内容: '{profile_text[:60]}...'")
                                    print(f"      ✅ 解析结果: 网站简介: {profile_text[:40]}...")
                            else:
                                print(f"   ❌ 无Profile信息")
                    
                    else:
                        print("❌ 未找到搜索结果")
                        
                except json.JSONDecodeError as e:
                    print(f"❌ JSON解析错误: {e}")
            else:
                print(f"❌ 搜索返回了非字符串结果: {type(result)}")
        
        except Exception as e:
            print(f"❌ 搜索出错: {str(e)}")
        
        # 添加请求间隔
        if i < len(test_queries):
            print("⏳ 等待2秒...")
            import time
            time.sleep(2)
    
    # 显示统计结果
    print(f"\n📊 Profile字段统计:")
    print("=" * 40)
    print(f"总Profile数: {total_profiles}")
    print(f"字典格式: {dict_profiles} ({(dict_profiles/max(total_profiles,1)*100):.1f}%)")
    print(f"字符串格式: {string_profiles} ({(string_profiles/max(total_profiles,1)*100):.1f}%)")

def test_profile_structure_analysis():
    """分析profile字段的结构"""
    print(f"\n🏗️ Profile字段结构分析")
    print("=" * 60)
    
    # 模拟不同类型的profile数据进行测试
    test_profiles = [
        {
            "name": "Google DeepMind",
            "url": "https://deepmind.google/models/veo/",
            "long_name": "deepmind.google",
            "img": "https://imgs.search.brave.com/favicon.ico"
        },
        {
            "name": "OpenAI",
            "long_name": "openai.com"
        },
        {
            "long_name": "github.com"
        },
        "Wikipedia - The Free Encyclopedia",
        "Microsoft Corporation Official Website"
    ]
    
    print("🧪 测试不同Profile格式的解析:")
    
    for i, profile in enumerate(test_profiles, 1):
        print(f"\n📋 测试 {i}:")
        print(f"   原始数据: {profile}")
        
        # 模拟解析逻辑
        extra_info = []
        if isinstance(profile, dict):
            print(f"   类型: 字典格式")
            profile_name = profile.get('name', '')
            profile_long_name = profile.get('long_name', '')
            if profile_name:
                extra_info.append(f"网站: {profile_name}")
                print(f"   ✅ 使用name字段: {profile_name}")
            elif profile_long_name:
                extra_info.append(f"网站: {profile_long_name}")
                print(f"   ✅ 使用long_name字段: {profile_long_name}")
            else:
                print(f"   ⚠️ 无可用名称字段")
            
            # 显示其他可用字段
            other_fields = []
            if profile.get('url'):
                other_fields.append(f"URL: {profile.get('url')[:30]}...")
            if profile.get('img'):
                other_fields.append("有图标")
            if other_fields:
                print(f"   📋 其他字段: {'; '.join(other_fields)}")
        
        else:
            print(f"   类型: 字符串格式")
            profile_text = str(profile)
            extra_info.append(f"网站简介: {profile_text[:40]}...")
            print(f"   ✅ 截取前40字符: {profile_text[:40]}...")
        
        print(f"   🎯 最终输出: {'; '.join(extra_info)}")

def main():
    """主测试函数"""
    print("🧪 Brave搜索Profile字段解析优化测试")
    print("验证字典和字符串格式的profile字段解析")
    print("=" * 80)
    
    # 检查API Token
    brave_token = os.getenv('BRAVE_API_TOKEN')
    if not brave_token or brave_token == 'your_brave_api_token_here':
        print("❌ 错误：未配置BRAVE_API_TOKEN")
        return
    
    print(f"✅ API Token: {brave_token[:10]}...{brave_token[-5:]}")
    
    # 运行测试
    test_profile_parsing()
    test_profile_structure_analysis()
    
    print(f"\n🎉 Profile字段解析测试完成！")
    print("=" * 80)
    
    print(f"\n💡 优化总结:")
    print("✅ 支持字典格式profile解析 (name, long_name, url, img)")
    print("✅ 支持字符串格式profile解析 (自动截取前40字符)")
    print("✅ 智能字段优先级 (name > long_name > 原始字符串)")
    print("✅ 完整的字段信息保留 (URL、图标等)")
    print("✅ 向后兼容性保证 (支持旧格式)")
    
    print(f"\n🔧 解析逻辑:")
    print("1. 检测profile字段类型 (字典 vs 字符串)")
    print("2. 字典格式: 优先使用name字段，其次long_name字段")
    print("3. 字符串格式: 截取前40字符作为简介")
    print("4. 保留完整的profile对象供进一步处理")

if __name__ == "__main__":
    main()
