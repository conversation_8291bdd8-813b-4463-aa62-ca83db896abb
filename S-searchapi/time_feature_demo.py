#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
时间信息功能演示
展示搜索结果中的时间信息处理和显示功能
"""

import json
from datetime import datetime, timedelta
from demo import SearchService


def demonstrate_time_parsing():
    """演示时间解析功能"""
    print("🕒 时间解析功能演示")
    print("=" * 50)
    
    search = SearchService()
    
    # 测试不同格式的时间解析
    test_times = [
        ("2024-05-26T10:30:00Z", "ISO格式时间"),
        ("2024-05-26 10:30:00", "标准格式时间"),
        ("2024-05-26", "日期格式"),
        ("May 26, 2024", "英文日期格式"),
        ("2天前", "相对时间"),
        ("1小时前", "相对时间"),
        ("刚刚", "相对时间"),
        ("", "空时间"),
        ("invalid_time", "无效时间")
    ]
    
    print("测试各种时间格式的解析:")
    for time_str, description in test_times:
        print(f"\n📅 {description}: '{time_str}'")
        time_info = search._format_time(time_str, "demo")
        
        print(f"   原始时间: {time_info['raw_time']}")
        print(f"   格式化时间: {time_info['formatted_time']}")
        print(f"   相对时间: {time_info['relative_time']}")
        print(f"   时间戳: {time_info['timestamp']}")


def demonstrate_search_with_time():
    """演示带时间信息的搜索"""
    print("\n🔍 带时间信息的搜索演示")
    print("=" * 50)
    
    search = SearchService()
    
    # 执行搜索
    query = "最新科技新闻"
    print(f"搜索关键词: {query}")
    
    result = search.search(query)
    
    try:
        data = json.loads(result)
        results = data.get("results", [])
        
        if results:
            print(f"\n✅ 找到 {len(results)} 条结果:")
            
            for i, item in enumerate(results[:5], 1):
                print(f"\n{i}. 📰 {item.get('title', 'N/A')}")
                print(f"   🔗 {item.get('link', 'N/A')}")
                print(f"   📝 {item.get('snippet', 'N/A')[:100]}...")
                
                # 详细显示时间信息
                time_info = item.get('time_info', {})
                if time_info:
                    print(f"   ⏰ 时间信息:")
                    print(f"      - 原始时间: {time_info.get('raw_time', 'N/A')}")
                    print(f"      - 格式化时间: {time_info.get('formatted_time', 'N/A')}")
                    print(f"      - 相对时间: {time_info.get('relative_time', 'N/A')}")
                    if time_info.get('timestamp'):
                        print(f"      - 时间戳: {time_info.get('timestamp')}")
                else:
                    print(f"   ⏰ 时间信息: 未提供")
        else:
            print("❌ 未找到搜索结果")
            
    except json.JSONDecodeError as e:
        print(f"❌ JSON解析错误: {e}")


def demonstrate_time_filtering():
    """演示基于时间的结果过滤"""
    print("\n⏳ 基于时间的结果过滤演示")
    print("=" * 50)
    
    search = SearchService()
    
    # 执行搜索
    query = "人工智能发展"
    result = search.search(query)
    
    try:
        data = json.loads(result)
        results = data.get("results", [])
        
        if results:
            print(f"原始搜索结果: {len(results)} 条")
            
            # 按时间分类
            recent_results = []  # 最近的结果
            old_results = []     # 较旧的结果
            unknown_time = []    # 时间未知的结果
            
            now = datetime.now()
            
            for result in results:
                time_info = result.get('time_info', {})
                timestamp = time_info.get('timestamp')
                
                if timestamp:
                    result_time = datetime.fromtimestamp(timestamp)
                    time_diff = now - result_time
                    
                    if time_diff.days <= 7:  # 一周内
                        recent_results.append(result)
                    else:
                        old_results.append(result)
                else:
                    unknown_time.append(result)
            
            print(f"\n📊 时间分类统计:")
            print(f"   🆕 最近一周: {len(recent_results)} 条")
            print(f"   📅 一周以前: {len(old_results)} 条")
            print(f"   ❓ 时间未知: {len(unknown_time)} 条")
            
            # 显示最近的结果
            if recent_results:
                print(f"\n🆕 最近一周的结果:")
                for i, item in enumerate(recent_results[:3], 1):
                    time_info = item.get('time_info', {})
                    print(f"{i}. {item.get('title', 'N/A')[:50]}...")
                    print(f"   时间: {time_info.get('relative_time', 'N/A')}")
            
        else:
            print("❌ 未找到搜索结果")
            
    except json.JSONDecodeError as e:
        print(f"❌ JSON解析错误: {e}")


def demonstrate_time_sorting():
    """演示按时间排序功能"""
    print("\n📈 按时间排序演示")
    print("=" * 50)
    
    search = SearchService()
    
    # 执行搜索
    query = "技术新闻"
    result = search.search(query)
    
    try:
        data = json.loads(result)
        results = data.get("results", [])
        
        if results:
            print(f"原始搜索结果: {len(results)} 条")
            
            # 按时间排序（有时间戳的结果）
            results_with_time = []
            results_without_time = []
            
            for result in results:
                time_info = result.get('time_info', {})
                if time_info.get('timestamp'):
                    results_with_time.append(result)
                else:
                    results_without_time.append(result)
            
            # 按时间戳降序排序（最新的在前）
            results_with_time.sort(
                key=lambda x: x.get('time_info', {}).get('timestamp', 0), 
                reverse=True
            )
            
            print(f"\n📅 按时间排序的结果 (有时间信息的 {len(results_with_time)} 条):")
            
            for i, item in enumerate(results_with_time[:5], 1):
                time_info = item.get('time_info', {})
                print(f"\n{i}. {item.get('title', 'N/A')[:60]}...")
                print(f"   时间: {time_info.get('formatted_time', 'N/A')} ({time_info.get('relative_time', 'N/A')})")
            
            if results_without_time:
                print(f"\n❓ 无时间信息的结果: {len(results_without_time)} 条")
        
        else:
            print("❌ 未找到搜索结果")
            
    except json.JSONDecodeError as e:
        print(f"❌ JSON解析错误: {e}")


def demonstrate_time_statistics():
    """演示时间统计功能"""
    print("\n📊 时间统计演示")
    print("=" * 50)
    
    search = SearchService()
    
    # 执行搜索
    query = "科技趋势"
    result = search.search(query)
    
    try:
        data = json.loads(result)
        results = data.get("results", [])
        
        if results:
            print(f"搜索结果总数: {len(results)} 条")
            
            # 统计时间信息
            time_stats = {
                "有时间信息": 0,
                "无时间信息": 0,
                "今天": 0,
                "本周": 0,
                "本月": 0,
                "更早": 0
            }
            
            now = datetime.now()
            
            for result in results:
                time_info = result.get('time_info', {})
                timestamp = time_info.get('timestamp')
                
                if timestamp:
                    time_stats["有时间信息"] += 1
                    result_time = datetime.fromtimestamp(timestamp)
                    time_diff = now - result_time
                    
                    if time_diff.days == 0:
                        time_stats["今天"] += 1
                    elif time_diff.days <= 7:
                        time_stats["本周"] += 1
                    elif time_diff.days <= 30:
                        time_stats["本月"] += 1
                    else:
                        time_stats["更早"] += 1
                else:
                    time_stats["无时间信息"] += 1
            
            print(f"\n📈 时间分布统计:")
            for category, count in time_stats.items():
                percentage = (count / len(results)) * 100
                print(f"   {category}: {count} 条 ({percentage:.1f}%)")
            
            # 显示时间范围
            timestamps = [
                result.get('time_info', {}).get('timestamp')
                for result in results
                if result.get('time_info', {}).get('timestamp')
            ]
            
            if timestamps:
                earliest = datetime.fromtimestamp(min(timestamps))
                latest = datetime.fromtimestamp(max(timestamps))
                
                print(f"\n📅 时间范围:")
                print(f"   最早: {earliest.strftime('%Y-%m-%d %H:%M:%S')}")
                print(f"   最晚: {latest.strftime('%Y-%m-%d %H:%M:%S')}")
                print(f"   跨度: {(latest - earliest).days} 天")
        
        else:
            print("❌ 未找到搜索结果")
            
    except json.JSONDecodeError as e:
        print(f"❌ JSON解析错误: {e}")


def main():
    """主函数"""
    print("🕒 搜索结果时间信息功能演示")
    print("=" * 60)
    
    # 检查搜索服务配置
    search = SearchService()
    if not search.search_service:
        print("❌ 错误: 未配置搜索服务")
        print("请在.env文件中设置SEARCH_SERVICE")
        return
    
    print(f"✅ 当前搜索服务: {search.search_service}")
    print(f"✅ 最大结果数: {search.max_results}")
    
    try:
        # 运行各种演示
        demonstrate_time_parsing()
        
        demonstrate_search_with_time()
        
        demonstrate_time_filtering()
        
        demonstrate_time_sorting()
        
        demonstrate_time_statistics()
        
        print("\n🎉 时间功能演示完成!")
        print("\n💡 提示:")
        print("- 不同搜索服务提供的时间信息格式可能不同")
        print("- 某些搜索服务可能不提供时间信息")
        print("- 时间解析支持多种格式，包括相对时间")
        print("- 可以基于时间信息进行结果过滤和排序")
        
    except KeyboardInterrupt:
        print("\n❌ 用户中断执行")
    except Exception as e:
        print(f"❌ 演示过程中出错: {str(e)}")


if __name__ == "__main__":
    main()
