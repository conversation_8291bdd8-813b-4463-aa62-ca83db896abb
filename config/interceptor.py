
import urllib.parse
import json
import os
import time
from mitmproxy import ctx, http
from http.cookies import SimpleCookie

class WeChatInterceptor:
    def __init__(self):
        self.config_file = "config/wechat_credentials.json"
        os.makedirs("config", exist_ok=True)

    def response(self, flow: http.HTTPFlow) -> None:
        """拦截微信相关请求"""
        url = urllib.parse.unquote(flow.request.url)

        # 拦截微信公众号文章页面请求
        if "mp.weixin.qq.com/s" in url:
            self._extract_credentials(flow, url)

        # 拦截getappmsgext API请求
        elif "mp.weixin.qq.com/mp/getappmsgext" in url:
            self._log_api_request(flow, url)

    def _extract_credentials(self, flow, url):
        """从请求中提取认证信息"""
        try:
            # 获取请求头
            headers = {}
            for key, value in flow.request.headers.items():
                headers[key] = value

            # 获取Cookie
            cookies_str = "; ".join([f"{key}={value}" for key, value in flow.request.cookies.items()])

            # 解析appmsg_token
            cookie_obj = SimpleCookie()
            cookie_obj.load(cookies_str)
            appmsg_token = cookie_obj.get('appmsg_token')
            appmsg_token_value = appmsg_token.value if appmsg_token else None

            if appmsg_token_value:
                # 保存认证信息
                credentials = {
                    "appmsg_token": appmsg_token_value,
                    "cookies": cookies_str,
                    "headers": headers,
                    "extracted_time": time.time(),
                    "source_url": url
                }

                with open(self.config_file, 'w', encoding='utf-8') as f:
                    json.dump(credentials, f, ensure_ascii=False, indent=4)

                ctx.log.info(f"✅ 成功提取微信认证信息: {appmsg_token_value[:20]}...")

        except Exception as e:
            ctx.log.error(f"❌ 提取认证信息失败: {e}")

    def _log_api_request(self, flow, url):
        """记录API请求信息"""
        try:
            response_data = flow.response.json()
            ctx.log.info(f"📊 API响应: {json.dumps(response_data, ensure_ascii=False)[:200]}...")
        except:
            pass

addons = [WeChatInterceptor()]
